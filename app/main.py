import argparse
import os
import sys
from typing import Dict, Optional

import matplotlib.pyplot as plt
import seaborn as sns
import services.file.open_and_edit_files as open_and_edit_files
import services.monitoring.server_logs as server_logs
import services.training.load_and_save_df as load_and_save_df
import services.training.train_model_and_forecast as train_model_and_forecast
from dotenv import load_dotenv
from services.logger.logging_service import get_logger, load_config

load_dotenv()
load_config()
logger = get_logger(__name__)
color_pal = sns.color_palette()
plt.style.use("fivethirtyeight")

directory_path: Optional[str] = os.getenv("directory_path")

env_vars: Dict[str, Optional[str]] = {
    "directory_path": directory_path,
}

# Check if environment variables are missing
for var_name, var_value in env_vars.items():
    if var_value is None:
        logger.error(f"Error: Missing required environment variable '{var_name}'")
        sys.exit(1)

saved_data_format = ["user_count", "y", "yhat", "yhat_lower", "yhat_upper"]


def main() -> None:
    """
    Script to manage server logs, load data, and train models

    Usage:
        python main.py [command] [--days <days>]

    Arguments:
        command                 Command to execute: 'log' for server log, 'load' to load data, 'train' to train models, 'transform' to process data

    Options:
        --days <days>           Number of days to use for training or transformation (default: 45)
        --freq <freq>           Data frequency in minutes (default: 5)
    """
    logger.info("Starting main script...")

    parser = argparse.ArgumentParser(description="Script to manage server logs, load data and train models.")

    parser.add_argument(
        "command",
        choices=["log", "load", "train", "load_and_train"],
        help="Command to execute: 'log' for server log, 'load' to load data, 'train' to train models, 'load_and_train' to both load then train",
    )

    parser.add_argument(
        "--days",
        type=int,
        default=45,
        help="Number of days to use for training or transformation (default: 45)",
    )

    args = parser.parse_args()

    command: str = args.command
    days_number: int = args.days

    # data frequency
    freq = 5
    int_data_freq: float = freq / 60  # 60/60 is 1 hour, 20/60 is 20 minutes, time bucket size
    data_freq: str = f"{round(int_data_freq * 60)}min"

    logger.info(f"Command: {command}, Days: {days_number}")

    if command == "log":
        logger.info("Executing 'log' command...")
        server_logs.server_logs(int_data_freq)
    # both load and train at the same time, easier for cron
    elif command == "load_and_train":
        logger.info(f"Executing 'load_and_train' command with {days_number} days...")

        logger.info("Loading data...")
        load_and_save_df.load_data(directory_path)
        logger.info("Loading data is finished...")

        logger.info("Before training, copying blacklist file from remote server...")
        open_and_edit_files.copy_blacklist_from_remote()
        logger.info("Blacklist has been copy, training is starting now...")
        train_model_and_forecast.train_model_and_forecast(directory_path, days_number, data_freq)
        logger.info("Training complete, syncing files post-training...")
        open_and_edit_files.rsync_end_of_training()
    # if you want to separately load or train
    elif command == "load":
        logger.info("Executing 'load' command...")
        load_and_save_df.load_data(directory_path)
    elif command == "train":
        logger.info(f"Executing 'train' command with {days_number} days...")
        logger.info("Before training, copying blacklist file from remote server...")
        open_and_edit_files.copy_blacklist_from_remote()
        logger.info("Blacklist has been copy, training is starting now...")
        train_model_and_forecast.train_model_and_forecast(directory_path, days_number, data_freq)
        logger.info("Training complete, syncing files post-training...")
        open_and_edit_files.rsync_end_of_training()
    else:
        logger.error("Unrecognized command")
        sys.exit(1)

    logger.info("Main script execution finished.")


if __name__ == "__main__":
    main()
