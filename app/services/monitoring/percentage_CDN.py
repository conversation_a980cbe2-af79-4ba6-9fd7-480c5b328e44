import numpy as np
import pandas as pd
from services.logger.logging_service import get_logger

logger = get_logger(__name__)


class PercentageCDN:
    def __init__(self):
        self.df = None
        logger.info("PercentageCDN class initialized.")

    def cdn_percentage(self, df: pd.DataFrame) -> None:
        """
        Calculates the CDN percentage for high and valid status in the given DataFrame.

        Args:
            df (pd.DataFrame): Input DataFrame with 'status' and 'cdn' columns.
        """
        logger.info("Starting CDN percentage calculation.")

        try:
            df["status"] = pd.to_numeric(df["status"], errors="coerce")
            logger.info("Converted status column to numeric.")
        except Exception as e:
            logger.error(f"Error converting status column to numeric: {e}")
            raise

        # Filter for high status (>399)
        df_high_status = df[df["status"] > 399].reset_index(drop=True)
        logger.info(f"Filtered {len(df_high_status)} rows for high status (>399).")

        # Filter for valid status (200-299)
        df_low_status = df[(df["status"] >= 200) & (df["status"] <= 299)].reset_index(drop=True)
        logger.info(f"Filtered {len(df_low_status)} rows for valid status (200-299).")

        # Define the regex pattern to capture the name and IP
        regex_pattern = r"nginx--(?P<name>[\w.\-#]+)--(?P<ip>(?:\d{1,3}\.){3}\d{1,3})"

        logger.info("Extracting 'name' and 'ip' from 'cdn' column using regex.")
        # Extract name and IP for both high and low status using vectorized operation
        for subset in [df_high_status, df_low_status]:
            subset[["name", "ip"]] = subset["cdn"].str.extract(regex_pattern)

            # Handle missing matches by directly assigning back
            subset["name"] = subset["name"].fillna("error_cdn_name")
            subset["ip"] = subset["ip"].fillna("error_cdn_ip")

        # Group high-status data by service, status, name, and IP
        grouped_high_status = df_high_status.groupby(["service", "status", "name", "ip"])["remote_addr"].nunique().reset_index(name="cdn_count")
        logger.info("Grouped high status data by service, status, name, and IP.")

        total_counts_high_status = grouped_high_status.groupby(["service", "status"])["cdn_count"].transform("sum")
        grouped_high_status["percentage"] = (grouped_high_status["cdn_count"] / total_counts_high_status * 100).round(2)

        # Group valid-status data by service, name, and IP (excluding status for aggregation)
        grouped_low_status = df_low_status.groupby(["service", "name", "ip"])["remote_addr"].nunique().reset_index(name="valid_cdn_count")

        # Calculate total counts per service
        total_counts_low_status = grouped_low_status.groupby("service")["valid_cdn_count"].transform("sum")
        # Calculate valid_percentage_count
        grouped_low_status["valid_percentage_count"] = np.where(
            total_counts_low_status == 0,
            100,  # Assign 100 if the total is 0
            (grouped_low_status["valid_cdn_count"] / total_counts_low_status * 100).round(2),
        )

        logger.info("Merged high-status and valid-status data.")
        # Merge high-status and valid-status data
        merged_df = pd.merge(
            grouped_high_status,
            grouped_low_status,
            on=["service", "name", "ip"],
            how="left",
        )

        # Fill NaN values for valid status columns where no valid status data exists
        merged_df["valid_cdn_count"] = merged_df["valid_cdn_count"].fillna(0).astype(int)
        merged_df["valid_percentage_count"] = merged_df["valid_percentage_count"].fillna(100)

        self.df = merged_df
        logger.info(f"CDN percentage calculation complete. Merged dataframe with {len(self.df)} rows.")

    def filter_by_service_and_status(self, service: str, status: str) -> pd.DataFrame:
        """
        Filters the DataFrame for rows matching the given service and status.

        Args:
            service (str): The service to filter by.
            status (str): The status to filter by.

        Returns:
            pd.DataFrame: A DataFrame containing all rows matching the specified service and status.
        """
        logger.info(f"Filtering data for service: {service}, status: {status}")

        filtered_df = self.df[(self.df["service"] == service) & (self.df["status"] == status)]
        logger.info(f"Filtered {len(filtered_df)} rows.")

        return self.__format_cdn(filtered_df)

    def __format_cdn(self, df: pd.DataFrame) -> list[dict]:
        """
        Formats the CDN DataFrame into a list of dictionaries.

        Args:
            df (pd.DataFrame): The CDN DataFrame.

        Returns:
            list[dict]: A list of dictionaries containing the formatted CDN data.
        """
        logger.info("Formatting the filtered CDN DataFrame into a list of dictionaries.")

        formatted = []
        for _, row in df.iterrows():
            formatted.append(
                {
                    "name": row["name"],
                    "ip": row["ip"],
                    "cdn_count": row["cdn_count"],
                    "percentage": row["percentage"],
                    "valid_cdn_count": row["valid_cdn_count"],
                    "valid_percentage_count": row["valid_percentage_count"],
                }
            )

        logger.info(f"Formatted {len(formatted)} rows into dictionaries.")
        return formatted
