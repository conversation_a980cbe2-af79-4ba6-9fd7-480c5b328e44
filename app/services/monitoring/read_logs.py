import re
import subprocess
from io import StringIO

import pandas as pd
from services.logger.logging_service import get_logger

logger = get_logger(__name__)


def run_command(command: str, input_data: str = None) -> str:
    """
    Executes a command in the shell and returns the output as a string.

    Args:
        command (str): The command to be executed in the shell.
        input_data (str, optional): Input data to be passed to the command. Defaults to None.

    Returns:
        str: The output of the command as a string.
    """
    logger.info(f"Running command: {command}")

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, input=input_data)
        logger.info(f"Command executed successfully. Output (first 100): {result.stdout[:100]}...")  # log first 100 chars of output
        return result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"Error while running command: {e.stderr}")
        raise


def parse_output(output: str, column_clients: str) -> pd.DataFrame:
    """
    Parses the output string and returns a pandas DataFrame.

    Parameters:
    - output (str): The output string to be parsed.
    - column_clients (list): A list of column clients for the DataFrame.

    Returns:
    - df (pandas.DataFrame): The parsed DataFrame.
    """
    logger.debug("Parsing output to DataFrame...")
    try:
        df = pd.read_csv(StringIO(output), sep=r"\s+", header=None, names=column_clients)
        logger.info(f"Parsed DataFrame with {len(df)} rows.")
        return df
    except Exception as e:
        logger.error(f"Failed to parse output: {e}")
        raise


def read_logs(path: str) -> pd.DataFrame:
    """
    Reads logs from the specified path and returns a parsed DataFrame.

    Args:
        path (str): The path to the log files.

    Returns:
        pd.DataFrame: A DataFrame with the log data.
    """
    logger.info(f"Reading logs from path: {path}")

    main_regex = r"""'"[0-9]{3}"/'"""
    command_main = f"rg -I --with-filename {main_regex} {path}/nginx* {path}/*ovp*"

    try:
        process_main = subprocess.Popen(command_main, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output, error = process_main.communicate()

        if process_main.returncode != 0:
            logger.error(f"Error while reading logs: {error.decode('utf-8')}")
            return pd.DataFrame()  # Return an empty DataFrame in case of error

        log_lines = output.decode("utf-8").splitlines()
        logger.info(f"Successfully read {len(log_lines)} log lines.")

        # Regex patterns to match desired log data
        client_pattern = re.compile(r'"([^"]*?)"[0-9]{4}-')
        status_pattern = re.compile(r'"([0-9]{3})"/')
        remote_addr_pattern = re.compile(r':00"((?:[0-9]{1,3}\.){3}[0-9]{1,3})"')

        data = []
        for line in log_lines:
            try:
                # Extract the filename and log content
                file_name, log_content = line.split(":", 1)
            except ValueError:
                logger.warning(f"Unexpected line format: {line}")
                continue

            # Match patterns in the log content
            client_match = client_pattern.search(log_content)
            status_match = status_pattern.search(log_content)
            remote_addr_match = remote_addr_pattern.search(log_content)

            if client_match and status_match and remote_addr_match:
                data.append(
                    {
                        "cdn": file_name,
                        "service": client_match.group(1),
                        "status": status_match.group(1),
                        "remote_addr": remote_addr_match.group(1),
                    }
                )

        # Create DataFrame
        df = pd.DataFrame(data)
        df.drop_duplicates(inplace=True)

        logger.info(f"Parsed DataFrame with {len(df)} rows after removing duplicates.")
        return df
    except Exception as e:
        logger.error(f"Error while processing logs: {e}")
        return pd.DataFrame()  # Return an empty DataFrame in case of error


def get_log_counts(joined_df: pd.DataFrame, date: str) -> pd.DataFrame:
    """
    Calculates the count of unique users and problem occurrences in the given joined_df DataFrame.

    Args:
        joined_df (pd.DataFrame): The DataFrame containing the logs data.
        date (str): The date for which the counts are calculated.

    Returns:
        pd.DataFrame: A DataFrame with columns 'service', 'status', 'problem_count', 'user_count', and 'date'.
    """
    logger.info(f"Calculating log counts for date: {date}")

    try:
        df_user_count = joined_df.groupby("service")["remote_addr"].nunique().reset_index(name="user_count")
        df_problem_count = joined_df.groupby(["service", "status"])["remote_addr"].nunique().reset_index(name="problem_count")

        joined_df = df_problem_count.merge(df_user_count, on="service")
        joined_df["date"] = date

        logger.info(f"Log counts calculated for {len(joined_df)} records.")
        return joined_df
    except Exception as e:
        logger.error(f"Error while calculating log counts: {e}")
        return pd.DataFrame()  # Return an empty DataFrame in case of error
