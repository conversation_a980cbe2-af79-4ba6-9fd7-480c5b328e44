import numpy as np
import pandas as pd
from services.logger.logging_service import get_logger

logger = get_logger(__name__)


def detect_outliers(
    df_merged: pd.DataFrame,
    sensitivity_factor: float,
    int_data_freq: int,
    previous_error: int,
) -> pd.DataFrame:
    """
    Detects outliers in error configurations using a trained model and historical data.

    Args:
        df_merged (pd.DataFrame): The input DataFrame containing the columns:
            - 'y': Observed values.
            - 'yhat': Predicted values.
            - 'user_count': Number of users.
            - 'problem_count': Number of problems.
        sensitivity_factor (float): A multiplier used to calculate the threshold for detecting outliers.
        int_data_freq (int): The frequency of data points (e.g., number of intervals per day).
        previous_error (int): Previous error count, used to flag certain outliers as "sure outliers."

    Returns:
        pd.DataFrame: The updated DataFrame with additional columns:
            - 'residual': The difference between observed and predicted values ('y' - 'yhat').
            - 'residual_mean': Rolling mean of residuals over the defined window size.
            - 'residual_std': Rolling standard deviation of residuals over the defined window size.
            - 'threshold': Threshold for outlier detection based on sensitivity_factor.
            - 'is_outlier': Boolean flag indicating whether a row is an outlier.
            - 'is_for_sure_outlier': Boolean flag indicating whether a row is a sure outlier.
    """
    logger.info(f"Starting outlier detection with sensitivity factor {sensitivity_factor}.")

    if sensitivity_factor == 0:
        logger.warning("Sensitivity factor is zero. No outliers will be detected.")
        df_merged["is_outlier"] = False
        df_merged["is_for_sure_outlier"] = False
        df_merged["threshold"] = 0
        return df_merged

    # Calculate residuals
    df_merged["residual"] = df_merged["y"] - df_merged["yhat"]
    logger.debug("Residuals calculated: y - yhat")

    window_size = round(24 * 7 * 5 / int_data_freq)  # 5 weeks
    logger.debug(f"Window size for rolling mean and std is {window_size}.")

    df_merged["residual_mean"] = df_merged["residual"].rolling(window=window_size).mean()
    df_merged["residual_std"] = df_merged["residual"].rolling(window=window_size).std()

    df_merged["threshold"] = (df_merged["residual_mean"] + sensitivity_factor * df_merged["residual_std"]).shift(periods=1)

    df_merged["threshold"] = np.where(df_merged["threshold"] > 100, 90, df_merged["threshold"])
    logger.debug("Threshold calculated and adjusted if greater than 100.")

    df_merged["is_outlier"] = (
        (df_merged["y"] > df_merged["yhat"] * (1 + sensitivity_factor / 40))
        & (df_merged["residual"] > 4)
        & (df_merged["y"] > df_merged["threshold"])
        & (df_merged["user_count"] > 3)
        & (df_merged["problem_count"] > 2)
    )

    df_merged["is_for_sure_outlier"] = df_merged["is_outlier"] & (previous_error is not None)
    logger.debug("Sure outliers determined based on previous_error.")

    return df_merged


def fix_thresholds_detect_outliers(df: pd.DataFrame, threshold: int) -> bool:
    """
    Detects outliers in fix error configs based on a given threshold.

    Args:
        df (pd.DataFrame): The time series dataframe to be analyzed.
        threshold (int): The threshold value for outlier detection.

    Returns:
        bool: True if the number of problem counts in the last row of the dataframe is greater than the threshold, False otherwise.
    """
    if threshold == 0:
        logger.warning("Sensitivity factor is zero. No outliers will be detected.")
        return False
    return df["problem_count"].iloc[-1] >= threshold


def detect_unknown_outliers(df: pd.DataFrame, threshold: int) -> bool:
    """
    Detects outliers in unknown configs based on a given threshold.

    Args:
        df (pd.DataFrame): The time series dataframe to be analyzed.
        threshold (int): The threshold value for outlier detection.

    Returns:
        bool: True if the number of problem counts in the last row of the dataframe is greater than the threshold, False otherwise.
    """
    if threshold == 0:
        logger.warning("Sensitivity factor is zero. No outliers will be detected.")
        return False
    return df["problem_count"].iloc[-1] >= threshold


def detect_manual_outliers(df: pd.DataFrame, threshold: int) -> bool:
    """
    Detects outliers in manual configs based on a given threshold.

    Args:
        df (pd.DataFrame): The time series dataframe to be analyzed.
        threshold (int): The threshold value for outlier detection.

    Returns:
        bool: True if the number of problem counts in the last row of the dataframe is greater than the threshold, False otherwise.
    """
    return df["problem_count"].iloc[-1] >= threshold
