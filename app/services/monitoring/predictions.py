import os
import sys
from typing import Tuple

import multiprocess as mp
import numpy as np
import pandas as pd
import services.file.open_and_edit_files as open_and_edit_files
from services.logger.logging_service import get_logger

logger = get_logger(__name__)

save_model_path = os.getenv("save_model_path")
if not save_model_path:
    logger.error("Error: Missing required environment variables 'save_model_path'")
    sys.exit(1)


def make_predictions(df: pd.DataFrame, pairs: tuple[pd.DataFrame, str, int]) -> dict:
    """
    Make predictions for time series data.

    Args:
        df (pd.DataFrame): The input DataFrame containing time series data.
        pairs (tuple[pd.DataFrame, str, int]): A tuple containing a DataFrame, a string, and an integer.

    Returns:
        dict: A dictionary containing the results of the predictions.
    """
    logger.info(f"Starting prediction process with {len(pairs)} pairs.")

    # Add features to df
    df["day_of_week"] = df["date"].dt.dayofweek
    df["hour"] = df["date"].dt.hour
    df["user_count"] = df["user_count"].astype(float)

    df.rename(columns={"date": "ds", "problem_count_ratio": "y"}, inplace=True)

    results = {}
    num_processes = mp.cpu_count()
    chunk_size = max(1, len(pairs) // (num_processes * 2))

    logger.info("Preparing multiprocessing pool")
    try:
        with mp.Pool(processes=num_processes) as pool:
            for result in pool.imap_unordered(predict, pairs, chunksize=chunk_size):
                ((service, status), df_pred) = result
                if df_pred is not None:
                    df_pred.index = df_pred["ds"]
                    if (service, status) not in results:
                        results[(service, status)] = df_pred
                    else:
                        results[(service, status)] = pd.concat([results[(service, status)], df_pred])
        logger.info("Prediction process completed successfully.")
    except Exception as e:
        logger.error(f"An error occurred during multiprocessing of predict: {e}")

    logger.info(f"Total predictions processed: {len(results)}")
    return results


def predict(args: tuple[pd.DataFrame, str, int]) -> Tuple[Tuple[str, int], pd.DataFrame]:
    """
    Predicts time series data using a Prophet model.

    Args:
        args (tuple[pd.DataFrame, str, int]): A tuple containing the following arguments:
            - df (pd.DataFrame): The input DataFrame containing the time series data.
            - service (str): The service name.
            - status (int): The status value.

    Returns:
        pd.DataFrame: The DataFrame containing the predicted values.
    """
    df, service, status = args
    logger.info(f"Starting prediction for service '{service}' with status {status}.")

    new_forecast_df = df[(df["service"] == service) & (df["status"] == status)]

    logger.debug(f"Applying log transformation to 'y' and 'user_count' for {service} {status}.")
    new_forecast_df.loc[:, "y"] = np.log1p(new_forecast_df["y"])
    new_forecast_df.loc[:, "user_count"] = np.log1p(new_forecast_df["user_count"])

    model_path = f"{save_model_path}{service}_{status}.json"
    model = open_and_edit_files.read_model(model_path)

    if model is None:
        logger.error(f"Model for {service} {status} not found at {model_path}.")
        return ((service, status), None)

    logger.info(f"Model for {service} {status} loaded successfully.")

    new_forecast_df.reset_index(drop=True, inplace=True)

    forecast = model.predict(new_forecast_df)

    # Inverse the log transformation for 'yhat' and 'yhat_upper'/'yhat_lower'
    logger.debug("Reversing log transformation for forecasted 'yhat' values.")
    forecast["yhat"] = np.expm1(forecast["yhat"])
    forecast["yhat_upper"] = np.expm1(forecast["yhat_upper"])
    forecast["yhat_lower"] = np.expm1(forecast["yhat_lower"])

    new_forecast_df.loc[:, "y"] = np.expm1(new_forecast_df["y"])
    new_forecast_df.loc[:, "user_count"] = np.expm1(new_forecast_df["user_count"])

    df_merged = new_forecast_df.merge(forecast[["ds", "yhat", "yhat_lower", "yhat_upper"]], on="ds", how="left")

    logger.info(f"Prediction for {service} {status} completed successfully.")
    return ((service, status), df_merged)
