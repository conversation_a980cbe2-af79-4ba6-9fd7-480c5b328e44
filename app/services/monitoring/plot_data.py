import os
from math import sqrt

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import prophet.models
from prophet.utilities import regressor_coefficients
from services.logger.logging_service import get_logger
# fmt: off
from sklearn.metrics import (mean_absolute_error,
                             mean_absolute_percentage_error,
                             median_absolute_error)

# fmt: on
logger = get_logger(__name__)


def plot_prophet_result(
    df_merged: pd.DataFrame,
    client: str,
    status: str,
    plot_save_path: str = None,
) -> None:
    """
    Plots the Prophet result for time series data with highlighted outliers.

    Args:
        df_merged (pd.DataFrame): The merged dataframe containing the time series data.
        client (str): The client name.
        status (str): The status of the data.
        plot_save_path (str, optional): The path to save the plot as an image file. Default is None.

    Returns:
        None
    """
    logger.info(f"Generating Prophet plot for client: {client}, status: {status}")

    plt.figure(figsize=(15, 7))

    # Plot actual data
    plt.plot(
        df_merged["ds"],
        df_merged["y"],
        linestyle="-",
        label="Actual - problem count/user count ratio",
        color="blue",
        zorder=2,
    )

    # Plot forecasted data
    plt.plot(
        df_merged["ds"],
        df_merged["yhat"],
        linestyle="-",
        label="Forecast - prediction",
        color="orange",
        zorder=2,
    )

    # Plot moving threshold
    plt.plot(
        df_merged["ds"],
        df_merged["threshold"],
        linestyle="-",
        label="Moving threshold",
        color="purple",
        linewidth=1,
        zorder=2,
    )

    # Highlight outliers
    outliers = df_merged[df_merged["is_outlier"]]
    plt.scatter(outliers["ds"], outliers["y"], color="red", label="Outliers", zorder=3)

    # Highlight combined outliers
    outliers = df_merged[df_merged["is_for_sure_outlier"]]
    plt.scatter(
        outliers["ds"],
        outliers["y"],
        color="green",
        label="Outliers combined",
        zorder=4,
    )

    # Add labels and title
    plt.xlabel("Date")
    plt.ylabel("Problem Count ratio")
    plt.title(f"Problem Count for Client: {client}, Status: {status}")
    plt.legend()
    plt.grid(True)

    if plot_save_path:
        if not os.path.exists(plot_save_path):
            os.makedirs(plot_save_path)
            logger.info(f"Created directory for saving plot at {plot_save_path}")

        # Save the plot to a file
        plt.savefig(f"{plot_save_path}{client}_{status}.png")
        logger.info(f"Plot saved at {plot_save_path}{client}_{status}.png")
    else:
        plt.show()

    plt.close()


def plot_residual(df_merged: pd.DataFrame) -> None:
    """
    Plot residuals over time.

    Parameters:
        df_merged (pd.DataFrame): The merged DataFrame containing the data.

    Returns:
        None
    """
    logger.info("Generating residual plot.")

    plt.figure(figsize=(10, 5))
    plt.scatter(df_merged["ds"], df_merged["residual"])
    plt.title("Residuals Over Time")
    plt.xlabel("Date")
    plt.ylabel("Residual")


def print_forecasting_score(df_merged: pd.DataFrame) -> None:
    """
    Print the forecasting score for a given DataFrame.

    Parameters:
        df_merged (pd.DataFrame): The merged DataFrame containing the forecasted and actual values.

    Returns:
        None
    """
    logger.info(f"Printing forecasting score for {df_merged['service'].iloc[0]}:{df_merged['status'].iloc[0]}")

    try:
        MAE = sqrt(int(mean_absolute_error(df_merged["yhat"], df_merged["y"])))
        logger.info(f"Mean Absolute Error (MAE): {np.round(MAE, 2)}")
    except Exception as e:
        logger.error(f"Error calculating MAE: {e}")

    try:
        MAD = sqrt(int(median_absolute_error(df_merged["yhat"], df_merged["y"])))
        logger.info(f"Median Absolute Error (MAD): {np.round(MAD, 2)}")
    except Exception as e:
        logger.error(f"Error calculating MAD: {e}")

    try:
        MAPE = mean_absolute_percentage_error(df_merged["yhat"], df_merged["y"])
        logger.info(f"Mean Absolute Percentage Error (MAPE): {np.round(MAPE, 2)} %")
    except Exception as e:
        logger.error(f"Error calculating MAPE: {e}")


def print_model_regressors(model: prophet.models) -> None:
    """
    Prints the coefficients of the regressors in the given model.

    Parameters:
        model (object): The model object containing the regressors.

    Returns:
        None
    """
    logger.info("Printing model regressors.")

    try:
        regressor_coef = regressor_coefficients(model)
        logger.info(regressor_coef[["regressor", "regressor_mode", "coef"]].sort_values("coef"))
    except Exception as e:
        logger.error(f"Error printing regressors: {e}")
