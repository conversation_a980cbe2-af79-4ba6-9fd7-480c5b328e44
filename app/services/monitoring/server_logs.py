import glob
import multiprocessing as mp
import os
import sys
from datetime import datetime as date
from datetime import timedel<PERSON>
from typing import Tuple

import pandas as pd

from ..file import open_and_edit_files
from ..logger.logging_service import get_logger
from ..managers.blacklist_manager import BlacklistManager
from ..managers.config_manager import ConfigManager
from ..managers.manual_config_manager import ManualConfigManager
from ..managers.unknown_config_manager import UnknownConfigManager
from ..managers.zabbix_manager import ZabbixManager
from ..training import data_preparation, load_and_save_df
from . import outlier_detection, plot_data, predictions, read_logs
from .percentage_CDN import PercentageCDN

pd.set_option("display.max_columns", None)
logger = get_logger(__name__)

plot_save_path: str = os.getenv("plot_save_path")
config_file_path: str = os.getenv("config_file_path")
config_file_for_fixerrors_path: str = os.getenv("config_file_for_fixerrors_path")
zabbix_result_file: str = os.getenv("zabbix_result_file")
sorted_data_path: str = os.getenv("sorted_data_path")
new_sorted_data_path: str = os.getenv("new_sorted_data_path")
logs_path: str = os.getenv("logs_path")
remote_logs_path: str = os.getenv("remote_logs_path")
remote_logs_port: str = os.getenv("remote_logs_port")
frontend_result_dir: str = os.getenv("frontend_result_dir")
blacklist_path: str = os.getenv("blacklist_path")
unknown_path: str = os.getenv("unknown_path")
save_model_path: str = os.getenv("save_model_path")
manual_config_path: str = os.getenv("manual_config_path")

env_vars = {
    "config_file_path": config_file_path,
    "zabbix_result_file": zabbix_result_file,
    "sorted_data_path": sorted_data_path,
    "new_sorted_data_path": new_sorted_data_path,
    "logs_path": logs_path,
    "plot_save_path": plot_save_path,
    "config_file_for_fixerrors_path": config_file_for_fixerrors_path,
    "remote_logs_path": remote_logs_path,
    "remote_logs_port": remote_logs_port,
    "frontend_result_dir": frontend_result_dir,
    "blacklist_path": blacklist_path,
    "unknown_path": unknown_path,
    "save_model_path": save_model_path,
    "manual_config_path": manual_config_path,
}

for var_name, var_value in env_vars.items():
    if var_value is None:
        logger.error(f"Error: Missing required environment variable '{var_name}'")
        sys.exit(1)

config_manager: ConfigManager = ConfigManager(config_file_path, config_file_for_fixerrors_path)
zabbix_manager: ZabbixManager = ZabbixManager(zabbix_result_file, frontend_result_dir)
blacklist_manager: BlacklistManager = BlacklistManager(blacklist_path)
unknown_config_manager: UnknownConfigManager = UnknownConfigManager(unknown_path)
manual_config_manager: ManualConfigManager = ManualConfigManager(manual_config_path)
percentage_cdn: PercentageCDN = PercentageCDN()
to_plot: bool = False


def is_newdir_ready(date_timestamp: date) -> bool:
    """
    Check if the directory corresponding to the given date timestamp exists.

    Args:
        date_timestamp: The date timestamp to check.

    Returns:
        bool: True if the directory exists, False otherwise.
    """
    logs_date_path = logs_path + data_preparation.zero_padding(date_timestamp.minute) + "/"
    logger.debug(f"Checking if directory exists: {logs_date_path}")
    return os.path.exists(logs_date_path)


def analyse_logs(base_df: pd.DataFrame, date_timestamp: date, int_data_freq: int) -> None:
    """
    Analyzes server logs for outlier detection in time series.

    Args:
        base_df (pd.DataFrame): The base dataframe containing the server logs data.
        date_timestamp (date): The date and time for which logs are to be analyzed.
        int_data_freq (int): The frequency of the data.

    Returns:
        None
    """

    log_path_list = [logs_path + data_preparation.zero_padding(date_timestamp.minute - 4 + i) + "/" for i in range(0, 5)]
    logger.debug(f"Log path list: {log_path_list}")

    date = f"{str(date_timestamp.year)}-{data_preparation.zero_padding(date_timestamp.month)}-"
    date += f"{data_preparation.zero_padding(date_timestamp.day)} {data_preparation.zero_padding(date_timestamp.hour)}:"
    date += f"{data_preparation.zero_padding(date_timestamp.minute)}:00"

    outlier_list = read_log_and_predict(date, log_path_list, base_df, int_data_freq)

    config_manager.save()
    unknown_config_manager.save()

    for service, status, df in outlier_list:
        if status < 500:
            logger.warning(
                f"{service}, {status}, y:{round(df['y'].iloc[-1])}, "
                f"yhat:{round(df['yhat'].iloc[-1])}, user_count:{round(df['user_count'].iloc[-1])}, "
                f"problem_count:{round(df['problem_count'].iloc[-1])}"
            )
        else:
            logger.warning(f"{service}:{status}:{round(df['problem_count'].iloc[-1])}")


def server_logs(int_data_freq: int) -> None:
    """
    Function to process server logs and perform outlier detection in time series.

    Returns:
        None
    """
    config_manager.load()
    unknown_config_manager.load()

    date_timestamp = date.now()

    # make it multiple of 5 (minus 1 because it starts at 0)
    if ((date_timestamp.minute + 1) % 5) != 0:
        minutes_to_subtract = (date_timestamp.minute + 1) % 5
        date_timestamp = date_timestamp - timedelta(minutes=minutes_to_subtract)

    actual_timestamp = date_timestamp - timedelta(minutes=4)
    open_and_edit_files.clean_directory(logs_path)
    base_df = load_dict_data(sorted_data_path)

    path_to_search_from = (
        remote_logs_path
        + str(date_timestamp.year)
        + "/"
        + data_preparation.zero_padding(date_timestamp.month)
        + "/"
        + data_preparation.zero_padding(date_timestamp.day)
        + "/"
        + data_preparation.zero_padding(date_timestamp.hour)
        + "/"
    )

    logger.info("starting log server")
    while True:
        while (actual_timestamp + timedelta(minutes=1)) < date.now() and actual_timestamp <= date_timestamp:
            open_and_edit_files.rsync_files(
                path_to_search_from + data_preparation.zero_padding(actual_timestamp.minute),
                logs_path,
                remote_logs_port,
            )
            actual_timestamp += timedelta(minutes=1)

        if is_newdir_ready(date_timestamp):
            logger.info("processing logs")
            config_manager.load()
            blacklist_manager.load()
            manual_config_manager.load()

            # Check if the new sorted_data directory exists
            if os.path.exists(new_sorted_data_path):
                logger.info(f"New sorted_data directory does exist: {new_sorted_data_path}")

                # Attempt to transfer the directory
                try:
                    open_and_edit_files.transfer_dir(sorted_data_path, new_sorted_data_path)
                    logger.info("Directory transfer successful.")

                    # Load dictionary data only if the directory transfer was successful
                    base_df = load_dict_data(sorted_data_path)
                    logger.info("Dictionary data loaded successfully.")

                except Exception as e:
                    logger.error(f"Directory transfer failed: {e}")

            analyse_logs(base_df, date_timestamp, int_data_freq)

            date_timestamp += timedelta(minutes=5)

            actual_timestamp = date_timestamp - timedelta(minutes=4)
            open_and_edit_files.clean_directory(logs_path)

            path_to_search_from = (
                remote_logs_path
                + str(date_timestamp.year)
                + "/"
                + data_preparation.zero_padding(date_timestamp.month)
                + "/"
                + data_preparation.zero_padding(date_timestamp.day)
                + "/"
                + data_preparation.zero_padding(date_timestamp.hour)
                + "/"
            )
            logger.info("end of processing logs")


def read_log_and_predict(date: str, log_path_list: list[str], base_df: pd.DataFrame, int_data_freq: int) -> list[Tuple[str, int, pd.DataFrame]]:
    """
    Reads log files, performs outlier detection in time series, and generates forecasted data.

    Parameters:
    date: str, date of log
    log_path_list: list[str], list of paths to log files to search from
    base_df: previous data

    Returns:
    list of (service, status, dataframe) for each level 2 outlier
    """

    outlier_list = []

    log_dfs = []

    num_processes = mp.cpu_count()

    logger.info("read_logs")
    try:
        with mp.Pool(processes=num_processes) as pool:
            chunk_size = max(1, len(log_path_list) // (num_processes * 2))
            for result in pool.imap_unordered(read_logs.read_logs, log_path_list, chunksize=chunk_size):
                if result is not None:
                    log_dfs.append(result)
    except Exception as e:
        logger.error(f"An error occurred during multiprocessing of read_logs: {e}")
    logger.info("finish with success read_logs")

    if len(log_dfs) == 0:
        logger.error("error while reading logs")
        return None

    joined_df = pd.concat(log_dfs)
    joined_df = joined_df[joined_df["service"] != ""]
    joined_df.drop_duplicates(inplace=True)
    cdn_df = joined_df.copy(deep=True)
    df = read_logs.get_log_counts(joined_df, pd.to_datetime(date).tz_localize(None))
    df["problem_count"] = pd.to_numeric(df["problem_count"], errors="coerce")
    df["status"] = pd.to_numeric(df["status"], errors="coerce")
    df = df[(df["status"] > 399)]
    df.set_index("date", inplace=True, drop=False)

    percentage_cdn.cdn_percentage(cdn_df)

    important_errors_df = df[(df["status"] >= 500)]
    important_unique_pairs = important_errors_df[["service", "status"]].drop_duplicates(subset=["service", "status"])

    important_pairs = []
    for service, status in important_unique_pairs.itertuples(index=False):
        if blacklist_manager.is_blacklisted(service):
            continue

        important_pairs.append(
            (
                important_errors_df[(important_errors_df["service"] == service) & (important_errors_df["status"] == status)],
                service,
                status,
            )
        )

    for group, service, status in important_pairs:
        isError = outlier_detection.fix_thresholds_detect_outliers(group, config_manager.get_fixerrors_settings(service, status))
        if isError:
            cdn = percentage_cdn.filter_by_service_and_status(service, status)
            outlier_list.append((service, status, group))
            zabbix_manager.set_result(
                service,
                status,
                zabbix_manager.max_alert,
                round(group["user_count"].iloc[-1]),
                round(group["problem_count"].iloc[-1] / group["user_count"].iloc[-1] * 100),
                round(group["problem_count"].iloc[-1]),
                cdn,
            )

    df = df[df["status"] < 500]
    df = data_preparation.ratio_calculation(df)

    unique_pairs = df[["service", "status"]].drop_duplicates(subset=["service", "status"])
    pairs = []
    for service, status in unique_pairs.itertuples(index=False):
        if blacklist_manager.is_blacklisted(service):
            continue

        threshold = manual_config_manager.get_value(service, status)
        if threshold:
            logger.error(f"manual_config_manager for {service} {status} not found.")
            new_forecast_df = df[(df["service"] == service) & (df["status"] == status)]  # .copy()
            if outlier_detection.detect_manual_outliers(new_forecast_df, threshold):
                cdn = percentage_cdn.filter_by_service_and_status(service, status)
                zabbix_manager.set_result(
                    service,
                    status,
                    zabbix_manager.max_alert,
                    round(new_forecast_df["user_count"].iloc[-1]),
                    round(new_forecast_df["problem_count_ratio"].iloc[-1]),
                    round(new_forecast_df["problem_count"].iloc[-1]),
                    cdn,
                )
            continue

        if not open_and_edit_files.read_model(f"{save_model_path}{service}_{status}.json"):
            logger.error(f"Model for {service} {status} not found.")
            threshold = unknown_config_manager.get_value(service, status)

            new_forecast_df = df[(df["service"] == service) & (df["status"] == status)]

            if outlier_detection.detect_unknown_outliers(new_forecast_df, threshold):
                cdn = percentage_cdn.filter_by_service_and_status(service, status)

                zabbix_manager.set_result(
                    service,
                    status,
                    zabbix_manager.max_alert,
                    round(new_forecast_df["user_count"].iloc[-1]),
                    round(new_forecast_df["problem_count_ratio"].iloc[-1]),
                    round(new_forecast_df["problem_count"].iloc[-1]),
                    cdn,
                )
            continue

        pairs.append((df, service, status))

    forecasted_df = predictions.make_predictions(df, pairs)

    df = base_df.copy() if base_df else pd.DataFrame()

    entry = []
    for key, forecasted_group in forecasted_df.items():
        service, status = key

        forecasted_group["user_count"] = forecasted_group["user_count"].astype(int)

        df[key] = pd.concat([df[key], forecasted_group])
        add_entry = (key, df[key], int_data_freq)
        entry.append(add_entry)

    logger.info(f"process_results: {len(entry)} pairs to process")
    try:
        with mp.Pool(processes=num_processes) as pool:
            chunk_size = max(1, len(entry) // (num_processes * 2))
            for result in pool.imap_unordered(process_results, entry, chunksize=chunk_size):
                service, status, df = result
                if df["is_for_sure_outlier"].iloc[-1]:
                    cdn = percentage_cdn.filter_by_service_and_status(service, status)
                    outlier_list.append((service, status, df))
                    zabbix_manager.set_result(
                        service,
                        status,
                        zabbix_manager.max_alert,
                        round(df["user_count"].iloc[-1]),
                        round(df["y"].iloc[-1]),
                        round(df["problem_count"].iloc[-1]),
                        cdn,
                    )
                elif df["is_outlier"].iloc[-1]:
                    cdn = percentage_cdn.filter_by_service_and_status(service, status)
                    zabbix_manager.set_result(
                        service,
                        status,
                        zabbix_manager.low_alert,
                        round(df["user_count"].iloc[-1]),
                        round(df["y"].iloc[-1]),
                        round(df["problem_count"].iloc[-1]),
                        cdn,
                    )
    except Exception as e:
        logger.error(f"An error occurred during multiprocessing of process_results: {e}")
    logger.info("finish with success process_results")

    zabbix_manager.save_result(date)

    return outlier_list


def process_results(arg: Tuple[Tuple[str, int], pd.DataFrame, int]) -> Tuple[str, int, pd.DataFrame, dict]:
    """
    Process the results of outlier detection in time series data.

    Args:
        arg (Tuple[Tuple[str, int], pd.DataFrame, int]): A tuple containing the following elements:
            - A tuple (service, status) representing the service and status.
            - A pandas DataFrame containing the merged data.
            - An integer representing the data frequency.

    Returns:
        Tuple[str, int, pd.DataFrame, dict]: A tuple containing the following elements:
            - The service name.
            - The status.
            - The DataFrame with outlier detection results.
            - A dictionary with additional information.
    """

    (service, status), df_merged, int_data_freq = arg

    sensitivity_factor = config_manager.get_settings(service, status)

    df = outlier_detection.detect_outliers(
        df_merged,
        sensitivity_factor,
        int_data_freq,
        zabbix_manager.has_already_be_seen(service, status),
    )

    if to_plot:
        plot_data.plot_prophet_result(df, service, status, plot_save_path=plot_save_path)

    return service, status, df


def load_dict_data(path: str) -> dict[pd.DataFrame]:
    """
    Load dictionary data from CSV files in a directory.
    """
    logger.info(f"Loading data from directory: {path}")

    if not os.path.exists(path):
        logger.error(f"Directory {path} does not exist")
        sys.exit(1)

    file_list = glob.glob(f"{path}*.csv")

    if not file_list:
        logger.error(f"No CSV files found in {path}")
        return None

    dataframes = {}
    logger.info("Starting to read CSV files...")
    try:
        with mp.Pool(processes=mp.cpu_count()) as pool:
            for result in pool.imap_unordered(load_and_save_df.read_df_dict_from_csv, file_list):
                if result is not None:
                    df, service, status = result
                    dataframes[(service, status)] = df
                    logger.info(f"Loaded data for service: {service}, status: {status}")
    except Exception as e:
        logger.error(f"Error during multiprocessing of read_df_dict_from_csv: {e}")

    logger.info("Finished reading CSV files.")
    return dataframes
