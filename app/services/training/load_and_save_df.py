import glob
import multiprocessing as mp
import os
import sys
from datetime import datetime, timedelta
from typing import List, Tuple

import pandas as pd
from services.logger.logging_service import get_logger
from services.training.Loader import Loader

logger = get_logger(__name__)


def save_year_data(save_dir: str, columns_to_load: List[str], lastdate: str) -> None:
    """
    Save year data to CSV files.

    Args:
        save_dir (str): The directory to save the CSV files.
        columns_to_load (List[str]): The list of columns to load from the data.
        lastdate (str): The last date to start processing from in 'YYYY-MM-DD' format.

    Returns:
        None
    """

    loader = Loader()
    if lastdate is None:
        start_date = datetime.today() - timedelta(days=365 * 2 + 1)
        logger.info(f"Start date not provided, defaulting to {start_date.strftime('%Y-%m-%d')}")
    else:
        # Parse the start date
        start_date = datetime.strptime(lastdate, "%Y-%m-%d")
        logger.info(f"Start date provided: {start_date.strftime('%Y-%m-%d')}")

    # Get today's date
    today = datetime.today()

    # Iterate through each day from start_date to today
    current_date = start_date + timedelta(days=1)
    while current_date < today - timedelta(days=1):
        logger.info(f"Processing day {current_date.strftime('%Y-%m-%d')}")

        # Increment the current date by one day
        year = current_date.year
        month = current_date.month
        day = current_date.day
        query = (
            f"WITH client_user_counts AS ("
            f'SELECT TIME_FLOOR(\\"__time\\", \'PT5M\') AS \\"date\\", \\"service_id\\", '
            f'APPROX_COUNT_DISTINCT(\\"remote_addr\\") AS \\"user_count\\" '
            f'FROM \\"all-filtered-nginx-log-entries-Xm\\" '
            f"WHERE (__time BETWEEN '{year}-{month}-{day} 00:00:00' AND '{year}-{month}-{day} 23:59:59') "
            f"AND \\\"service_id\\\" <> '' "
            f'GROUP BY TIME_FLOOR(\\"__time\\", \'PT5M\'), \\"service_id\\") '
            f'SELECT TIME_FLOOR(\\"__time\\", \'PT5M\') AS \\"date\\", '
            f'cu.\\"user_count\\", '
            f'al.\\"service_id\\", '
            f'al.\\"status\\", '
            f'APPROX_COUNT_DISTINCT(al.\\"remote_addr\\") AS \\"problem_count\\" '
            f'FROM \\"all-filtered-nginx-log-entries-Xm\\" al '
            f"JOIN client_user_counts cu "
            f'ON TIME_FLOOR(al.\\"__time\\", \'PT5M\') = cu.\\"date\\" '
            f'AND al.\\"service_id\\" = cu.\\"service_id\\" '
            f'WHERE al.\\"status\\" BETWEEN 400 AND 499 '
            f"AND (al.\\\"__time\\\" BETWEEN '{year}-{month}-{day} 00:00:00' AND '{year}-{month}-{day} 23:59:59') "
            f"AND al.\\\"service_id\\\" <> '' "
            f"GROUP BY TIME_FLOOR(al.\\\"__time\\\", 'PT5M'), "
            f'cu.\\"user_count\\", '
            f'al.\\"service_id\\", '
            f'al.\\"status\\" '
            f'ORDER BY cu.\\"user_count\\" DESC'
        )

        logger.info(f"Executing query for {current_date.strftime('%Y-%m-%d')}")

        data = loader.load_data(query)

        # to clean incomplete data
        if data is None or len(data["data"]) < 2:
            logger.error(f"Error fetching data from Druid for {current_date}")
            current_date += timedelta(days=1)
            continue

        # load into dataframe
        df = pd.DataFrame(data["data"], columns=columns_to_load)
        # delete naming column from JSON
        df = df.drop(df.index[0])
        # Set the 'date' column as the index
        df.set_index("date", inplace=True)
        # order the data by date
        df.sort_index(inplace=True)

        logger.info(f"Data successfully processed for {current_date.strftime('%Y-%m-%d')}")
        save_df_to_csv(df, current_date.strftime("%Y-%m-%d"), save_dir)
        current_date += timedelta(days=1)


def save_df_to_csv(df: pd.DataFrame, nameofdf: str, save_dir: str) -> None:
    """
    Save a DataFrame to a CSV file.

    Args:
        df (pd.DataFrame): The DataFrame to save.
        nameofdf (str): The name of the DataFrame.
        save_dir (str): The directory to save the DataFrame in.

    Returns:
        None
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        logger.info(f"Created directory {save_dir} to save CSV files")

    filename = f"{save_dir}{nameofdf}.csv"
    try:
        df.to_csv(filename, index=True)
        logger.info(f"Saved {nameofdf}.csv to {save_dir}")
    except Exception as e:
        logger.error(f"Error saving {nameofdf}.csv to {save_dir}: {e}")


def read_df_from_csv(directory_path: str) -> List[pd.DataFrame]:
    """
    Reads CSV files from a directory and returns a list of pandas DataFrames.

    Args:
        directory_path (str): The path to the directory containing the CSV files.

    Returns:
        List[pd.DataFrame]: A list of pandas DataFrames read from the CSV files.

    """
    num_processes = mp.cpu_count()

    if not os.path.exists(directory_path):
        logger.error(f"Directory {directory_path} does not exist")
        sys.exit(1)

    # Get list of all CSV files in the directory
    file_list = glob.glob(f"{directory_path}*.csv")

    if not file_list:
        logger.error(f"No CSV files found in {directory_path}")
        return []

    dataframes = []

    logger.info("Starting to read CSV files")
    try:
        with mp.Pool(processes=num_processes) as pool:
            for result in pool.imap_unordered(read_df_from_csv_file, file_list):
                if result is not None:
                    df, _ = result
                    dataframes.append(df)
    except Exception as e:
        logger.error(f"An error occurred during multiprocessing of read_df_from_csv_file: {e}")

    logger.info("Completed reading CSV files")
    return dataframes


def read_df_dict_from_csv(file_path: str) -> Tuple[pd.DataFrame, str, int]:
    """
    Reads a DataFrame and additional information from a CSV file.

    Args:
        file_path (str): The path to the CSV file.

    Returns:
        Tuple[pd.DataFrame, str, int]: A tuple containing the DataFrame, service, and status.
    """

    df, filename = read_df_from_csv_file(file_path)
    if df is None:
        logger.error(f"Error reading {file_path}")
        return None

    df["ds"] = df.index

    infos = filename.split("_")
    service = infos[len(infos) - 1 - 1].split("/")[1]
    status = int(infos[len(infos) - 1].split(".")[0])
    df["service"] = service
    df["status"] = status

    logger.info(f"Successfully processed {file_path}")
    return df, service, status


def read_df_from_csv_file(filename: str) -> Tuple[pd.DataFrame, str]:
    """
    Reads a DataFrame from a CSV file.

    Args:
        filename (str): The path to the CSV file.

    Returns:
        Tuple[pd.DataFrame, str]: A tuple containing the DataFrame and the filename.
    """

    try:
        df = pd.read_csv(filename, index_col=0, parse_dates=True)
        df.index = pd.to_datetime(df.index)
        df.index = df.index.tz_localize(None)
        logger.info(f"Successfully read {filename}")
        return df, filename
    except Exception as e:
        logger.error(f"Error reading {filename}: {e}")
        return None, None


def find_most_recent_date(dir_path: str) -> str:
    """
    Find the most recent date from date-named CSV files in the given directory.

    Args:
        dir_path (str): Path to the directory containing the CSV files.

    Returns:
        str: The most recent date as a string in the format YYYY-MM-DD.
    """
    if not os.path.exists(dir_path):
        logger.error(f"Directory {dir_path} does not exist")
        return None

    date_list = []

    # List all files in the directory
    for filename in os.listdir(dir_path):
        if filename.endswith(".csv"):
            try:
                # Extract the date part from the filename (assuming the format YYYY-MM-D.csv)
                date_str = filename.replace(".csv", "")
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                date_list.append(date_obj)
            except ValueError:
                # Skip files that don't match the expected date format
                logger.warning(f"Skipping file {filename} as it doesn't match the expected date format")

    # Find the most recent date
    if date_list:
        most_recent_date = max(date_list)
        logger.info(f"Most recent date found: {most_recent_date.strftime('%Y-%m-%d')}")
        return most_recent_date.strftime("%Y-%m-%d")
    else:
        logger.error("No valid date-named files found")
        return None  # No valid date-named files found


def load_data(path: str) -> None:
    """
    Load the data from the specified path and save the year data.

    Args:
        path (str): The path to the data file.

    Returns:
        None
    """
    lastdate = find_most_recent_date(path)
    logger.info(f"Proceeding to save year data starting from {lastdate}")
    save_year_data(path, ["date", "user_count", "service", "status", "problem_count"], lastdate)
