import json
import os

import requests
import urllib3
from services.logger.logging_service import get_logger

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = get_logger(__name__)


class Loader:
    """
    A class that handles loading data from Druid DB.

    Attributes:
        URLuserLogin (str): The URL for user login.
        URLlog (str): The URL for logging.
        username (str): The username for API authentication.
        password (str): The password for API authentication.
        token (str): The authorization token obtained after successful login.

    Methods:
        __init__(): Initializes the Loader class.
        login_to_API(): Logs in to the API and returns the response data.
        get_data_druid(token, query): Retrieves data from the API using the provided token and query.
        load_data(query): Loads data from the API using the provided query and returns the data.
    """

    def __init__(self):
        """
        Initializes the Loader object.
        """
        logger.info("Initializing Loader class.")

        self.URLuserLogin = "https://advancedanalytics2api.hexaglobe.com/user/login"
        self.URLlog = "https://advancedanalytics2api.hexaglobe.com/log/"
        self.username: str = os.getenv("username")
        self.password: str = os.getenv("password")

        if not self.username or not self.password:
            logger.error("Missing required environment variables 'username' or 'password'.")
            raise ValueError("Error: Missing required environment variables 'username' or 'password'.")

        logger.info("Logging in to the API.")
        try:
            self.token: str = json.loads(self.login_to_API())["Authorization"]
            logger.info("Successfully logged in to the API.")
        except KeyError as e:
            logger.error(f"Failed to retrieve authorization token: {e}")
            raise

    def login_to_API(self) -> str:
        """
        Logs in to the API using the provided username and password.

        Returns:
            str: The response data from the API.
        """
        logger.debug("Attempting login to API with username: %s", self.username)

        PARAMS = {"username": self.username, "password": self.password}
        HEADERS = {"accept": "application/json"}

        try:
            r = requests.post(url=self.URLuserLogin, headers=HEADERS, data=PARAMS, verify=False)
            r.raise_for_status()  # Raise an HTTPError for bad responses
            logger.debug("Login response received.")
            return r.text
        except requests.exceptions.RequestException as e:
            logger.error(f"Error during login to API: {e}")
            raise

    def get_data_druid(self, query: str) -> str:
        """
        Retrieves data from Druid using the provided token and query.

        Args:
            query (str): The query to be executed.

        Returns:
            str: The retrieved data from Druid.
        """
        logger.debug("Executing query on Druid: %s", query)

        PARAMS = "Request=(" + query + ")"
        HEADERS = {
            "accept": "application/json",
            "Authorization": self.token,
            "Content-Type": "application/x-www-form-urlencoded",
        }

        try:
            res = requests.post(url=self.URLlog, headers=HEADERS, data=PARAMS, verify=False)
            res.raise_for_status()  # Raise an HTTPError for bad responses
            logger.debug("Druid query executed successfully.")
            return res.text
        except requests.exceptions.RequestException as e:
            logger.error(f"Error executing query on Druid: {e}")
            raise

    def load_data(self, query: str) -> dict:
        """
        Loads data from Druid based on the given query.

        Parameters:
        - query (str): The query to be executed in Druid.

        Returns:
        - data (dict): The loaded data from Druid in dictionary format.
        """
        logger.info("Loading data from Druid with query: %s", query)

        try:
            data = json.loads(self.get_data_druid(query))
            logger.info("Data loaded successfully.")
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            data = None

        return data
