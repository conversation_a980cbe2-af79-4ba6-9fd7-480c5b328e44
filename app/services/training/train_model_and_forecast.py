import os
import sys
from datetime import timed<PERSON>ta

import multiprocess as mp
import numpy as np
import pandas as pd
import services.file.open_and_edit_files as open_and_edit_files
import services.training.data_preparation as data_preparation
import services.training.load_and_save_df as load_and_save_df
from main import saved_data_format
from prophet import Prophet
from services.logger.logging_service import get_logger
from services.managers.blacklist_manager import BlacklistManager

logger = get_logger(__name__)

training_save_model_path: str = os.getenv("training_save_model_path")
sorted_data_path_in_training_serv: str = os.getenv("sorted_data_path_in_training_serv")
zabbix_service_status_list_file_in_training_serv: str = os.getenv("zabbix_service_status_list_file_in_training_serv")
blacklist_path_copy: str = os.getenv("blacklist_path_copy")

env_vars = {
    "training_save_model_path": training_save_model_path,
    "sorted_data_path_in_training_serv": sorted_data_path_in_training_serv,
    "zabbix_service_status_list_file_in_training_serv": zabbix_service_status_list_file_in_training_serv,
    "blacklist_path_copy": blacklist_path_copy,
}

# Checking required environment variables
for var_name, var_value in env_vars.items():
    if var_value is None:
        logger.error(f"Error: Missing required environment variable '{var_name}'")
        sys.exit(1)

blacklist_manager: BlacklistManager = BlacklistManager(blacklist_path_copy)
save_forecast: bool = True


def train_model_and_forecast(path: str, days_number: int, data_freq: str) -> None:
    """
    Trains a model and performs a forecast on the given dataset.

    Args:
        path (str): The file path of the dataset.
        days_number (int): The number of days to forecast.
        data_freq (str): The frequency of the data.

    Returns:
        None
    """

    logger.info(f"Loading and cleaning data from path: {path}")
    df = load_and_clean_data(path, data_freq)

    # Filter out certain status codes
    logger.info("Filtering data to only include status codes between 400 and 499.")
    df = df[df["status"] > 399]
    df = df[df["status"] < 500]

    logger.info("Calculating ratios for data.")
    df = data_preparation.ratio_calculation(df)
    df.rename(columns={"problem_count_ratio": "y", "date": "ds"}, inplace=True)

    unique_pairs = df[["service", "status"]].drop_duplicates().reset_index(drop=True)

    logger.info("Generating list of unique service-status pairs for Zabbix.")
    open_and_edit_files.list_of_service_status_for_zabbix_json(zabbix_service_status_list_file_in_training_serv, unique_pairs)

    blacklist_manager.load()

    logger.info(f"Total unique (service, status) pairs: {len(unique_pairs)}")

    iter_on_pairs(unique_pairs, df, days_number)


def iter_on_pairs(unique_pairs: pd.DataFrame, df: pd.DataFrame, days_number: int) -> None:
    """
    Iterates over unique pairs of service and status in the given DataFrame and performs outlier detection on each pair.

    Args:
        unique_pairs (pd.DataFrame): DataFrame containing unique pairs of service and status.
        df (pd.DataFrame): DataFrame containing the data for outlier detection.
        days_number (int): The number of days to forecast.

    Returns:
        None
    """

    pairs = []

    # fill list of pairs only with not already processed pair
    for _, row in unique_pairs.iterrows():
        if blacklist_manager.is_blacklisted(row["service"]):
            logger.info(f"Skipping blacklisted service {row['service']}.")
            continue

        if not os.path.exists(f"{training_save_model_path}{row['service']}_{row['status']}.json"):
            logger.info(f"Adding pair ({row['service']}, {row['status']}) for processing.")
            pairs.append((df, row["service"], row["status"], days_number))

    num_processes = mp.cpu_count()
    chunk_size = max(1, len(pairs) // (num_processes * 2))

    logger.info(f"Starting model creation for {len(pairs)} pairs using {num_processes} processes.")
    count = 0
    try:
        with mp.Pool(processes=num_processes) as pool:
            for _ in pool.imap_unordered(create_model, pairs, chunksize=chunk_size):
                count += 1
                logger.info(f"Processed {count}/{len(pairs)} pairs.")
    except Exception as e:
        logger.error(f"Error during multiprocessing of model creation: {e}")
    logger.info("Completed model creation process.")


def create_model(args: tuple[pd.DataFrame, str, int, int]) -> None:
    """
    This function detects outliers for a specific service and status.
    It fits a Prophet model, makes predictions, calculates residuals,
    identifies outliers, and plots the results.

    Parameters:
    args (tuple): A tuple containing the following elements:
        - df (pd.DataFrame): The input DataFrame.
        - service (str): The service name.
        - status (int): The status code.
        - days_number (int): The number of days to forecast.

    Returns:
        None
    """
    df, service, status, days_number = args

    logger.info(f"Processing service {service}, status {status}.")

    df_pair = df[["ds", "y", "user_count"]][(df["service"] == service) & (df["status"] == status)].copy()

    df_pair["y"] = np.log1p(df_pair["y"])
    df_pair["user_count"] = np.log1p(df_pair["user_count"])

    df_pair["hour"] = df_pair["ds"].dt.hour
    df_pair["day_of_week"] = df_pair["ds"].dt.dayofweek

    model = initialize_model()

    regressors = [
        "user_count",
        "day_of_week",
        "hour",
    ]
    for reg in regressors:
        model.add_regressor(reg, mode="multiplicative")

    model.fit(df_pair[["ds", "y"] + regressors])

    future = df_pair[["ds"]].copy()
    future[regressors] = df_pair[regressors]

    logger.info(f"Training model for {service}, {status}")
    forecast = model.predict(future)

    forecast["yhat"] = np.expm1(forecast["yhat"])
    forecast["yhat_upper"] = np.expm1(forecast["yhat_upper"])
    forecast["yhat_lower"] = np.expm1(forecast["yhat_lower"])
    df_pair["y"] = np.expm1(df_pair["y"])
    df_pair["user_count"] = np.expm1(df_pair["user_count"])

    df_merged = df_pair.merge(forecast[["ds", "yhat", "yhat_lower", "yhat_upper"]], on="ds")

    if not os.path.exists(training_save_model_path):
        os.makedirs(training_save_model_path)
    open_and_edit_files.save_model(model, f"{training_save_model_path}{service}_{status}.json")

    if save_forecast:
        logger.info(f"Saving forecast for {service}, {status}.")
        max_date = df_merged["ds"].max()
        cutoff_date = max_date - timedelta(days=days_number)
        df_merged = df_merged[df_merged["ds"] > cutoff_date]
        df_merged.index = df_merged["ds"]
        load_and_save_df.save_df_to_csv(
            df_merged[saved_data_format],
            f"{service}_{status}",
            sorted_data_path_in_training_serv,
        )


def initialize_model() -> Prophet:
    """
    Initializes a Prophet model for time series forecasting.

    Returns:
        Prophet: The initialized Prophet model.
    """
    logger.info("Initializing Prophet model.")
    model = Prophet(
        seasonality_mode="multiplicative",
        interval_width=0.95,
        yearly_seasonality=False,
        daily_seasonality=False,
        weekly_seasonality=False,
    )

    model.add_seasonality(name="daily", period=1, fourier_order=40)
    model.add_seasonality(name="hourly", period=1 / 24, fourier_order=20)
    model.add_seasonality(name="weekly", period=7, fourier_order=40)

    return model


def load_and_clean_data(path: str, data_freq: str) -> pd.DataFrame:
    """
    Load and clean the data from the given path.

    Args:
        path (str): The path to the data file.
        data_freq (str): The frequency of the data.

    Returns:
        pd.DataFrame: The cleaned data.
    """
    logger.info(f"Loading data from path: {path}.")
    yearSeries = load_and_save_df.read_df_from_csv(path)
    df = pd.concat(yearSeries)

    df["date"] = df.index
    df.sort_index(inplace=True)

    num_processes = mp.cpu_count()
    df_filled = []
    min_date = df.index.min()
    max_date = df.index.max()
    full_date_range = pd.date_range(start=min_date, end=max_date, freq=data_freq)

    fill_entry = []
    for service, group in df.groupby(["service"]):
        service_df_user_count = group.drop_duplicates(subset="date")["user_count"]
        for status, status_group in group.groupby(["status"]):
            fill_entry.append(
                (
                    service_df_user_count,
                    full_date_range,
                    service[0],
                    status[0],
                    status_group,
                )
            )

    logger.info("Filling missing hours in data.")
    try:
        with mp.Pool(processes=num_processes) as pool:
            chunk_size = max(1, len(fill_entry) // (num_processes * 10))
            for result in pool.imap_unordered(
                data_preparation.fill_missing_hours,
                fill_entry,
                chunksize=chunk_size,
            ):
                if result is not None:
                    df_filled.append(result)
    except Exception as e:
        logger.error(f"Error during multiprocessing of fill_missing_hours: {e}")
    logger.info("Finished filling missing hours.")

    df = pd.concat(df_filled, ignore_index=True)
    df.sort_values(by=["date"], inplace=True, ignore_index=True)

    return df
