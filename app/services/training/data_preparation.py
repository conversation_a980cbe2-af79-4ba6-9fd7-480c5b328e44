from typing import <PERSON>ple

import numpy as np
import pandas as pd
from services.logger.logging_service import get_logger

logger = get_logger(__name__)


def fill_missing_hours(args: Tuple[pd.Series, pd.DataFrame, str, int, pd.DataFrame]) -> pd.DataFrame:
    """
    Fills missing hours in a time series data.

    Args:
        args (Tuple[pd.Series, pd.DataFrame, str, int, pd.DataFrame]): A tuple containing the following arguments:
            - user_count_df (pd.Series): The user count data.
            - full_date_range (pd.DataFrame): The full date range (each time bucket from first to last day).
            - client (str): The client name.
            - status (int): The status code.
            - group (pd.DataFrame): The main df.

    Returns:
        pd.DataFrame: The group data with missing hours filled.
    """
    logger.info(f"Started filling missing hours for client: {args[2]}, status: {args[3]}")

    user_count_df, full_date_range, client, status, group = args

    # Reindex the group DataFrame with the full date range
    logger.debug("Reindexing group DataFrame with full date range.")
    group = group.reindex(full_date_range, fill_value=None)

    # If no non-null user counts are present, return None
    if group["user_count"].notnull().sum() == 0:
        logger.warning("No user count data available, returning None.")
        return None

    group["service"] = client
    group["status"] = status

    # Backfill and forward fill user_count to fill missing values
    logger.debug("Filling missing user_count values using backfill and forward fill.")
    group["user_count"] = user_count_df
    group["user_count"] = group["user_count"].bfill()
    group["user_count"] = group["user_count"].ffill()

    # Fill missing problem count with 0
    group["problem_count"] = group["problem_count"].fillna(0)

    group["date"] = group.index
    logger.info(f"Completed filling missing hours for client: {client}, status: {status}")

    return group


def ratio_calculation(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculates the problem count ratio for each row in the DataFrame.

    Parameters:
    - df (pd.DataFrame): The input DataFrame containing the columns 'user_count' and 'problem_count'.

    Returns:
    - pd.DataFrame: The input DataFrame with an additional column 'problem_count_ratio' representing
        the ratio of problem count to user count, is 0 if no user.
    """
    logger.info("Started calculating problem count ratios.")

    # Calculate problem_count_ratio as percentage of problem count over user count
    df["problem_count_ratio"] = np.where(df["user_count"] == 0, 0, (df["problem_count"] / df["user_count"]) * 100)

    logger.debug("Problem count ratios calculated.")
    return df


def zero_padding(number: int) -> str:
    """
    Returns a string representation of the given number with zero padding if necessary.

    Parameters:
    - number (int): The number to be padded.

    Returns:
    - str: The string representation of the padded number.
    """
    logger.debug(f"Padding number {number} with leading zero if necessary.")

    padded_number = str(number) if number >= 10 else "0" + str(number)

    logger.debug(f"Padded number: {padded_number}")
    return padded_number
