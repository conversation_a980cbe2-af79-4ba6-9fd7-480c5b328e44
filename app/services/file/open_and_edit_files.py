import json
import os
import shutil
import subprocess
import sys
from datetime import timed<PERSON>ta
from typing import List

import multiprocess as mp
import pandas as pd
import prophet
import prophet.models
import services.monitoring.predictions as predictions
import services.training.data_preparation as data_preparation
import services.training.load_and_save_df as load_and_save_df
from prophet.serialize import model_from_json, model_to_json
from services.logger.logging_service import get_logger

logger = get_logger(__name__)

zabbix_service_status_list_file_in_training_serv: str = os.getenv("zabbix_service_status_list_file_in_training_serv")
training_save_model_path: str = os.getenv("training_save_model_path")
sorted_data_path_in_training_serv: str = os.getenv("sorted_data_path_in_training_serv")

to_rsync_zabbix_service_status_list_file: str = os.getenv("to_rsync_zabbix_service_status_list_file")
to_rsync_save_model_path: str = os.getenv("to_rsync_save_model_path")
to_rsync_sorted_data_path: str = os.getenv("to_rsync_sorted_data_path")
from_rsync_blacklist_path: str = os.getenv("from_rsync_blacklist_path")
blacklist_path_copy: str = os.getenv("blacklist_path_copy")

remote_monitoring_server: str = os.getenv("remote_monitoring_server")
remote_monitoring_port: str = os.getenv("remote_monitoring_port")

env_vars = {
    "sorted_data_path_in_training_serv": sorted_data_path_in_training_serv,
    "training_save_model_path": training_save_model_path,
    "zabbix_service_status_list_file_in_training_serv": zabbix_service_status_list_file_in_training_serv,
    "to_rsync_sorted_data_path": to_rsync_sorted_data_path,
    "to_rsync_save_model_path": to_rsync_save_model_path,
    "to_rsync_zabbix_service_status_list_file": to_rsync_zabbix_service_status_list_file,
    "remote_monitoring_server": remote_monitoring_server,
    "remote_monitoring_port": remote_monitoring_port,
    "from_rsync_blacklist_path": from_rsync_blacklist_path,
    "blacklist_path_copy": blacklist_path_copy,
}

for var_name, var_value in env_vars.items():
    if var_value is None:
        logger.error(f"Error: Missing required environment variable '{var_name}'")
        sys.exit(1)

std_multiplier_ref: int = 5
percentage_threshold_ref: float = 1.5


def save_model(model: prophet.models, file_name: str) -> bool:
    """
    Save the model to a file

    Args:
        model (prophet.models): The loaded model.
        file_name (str): The path to save the model.

    Returns:
        bool: True if the model is successfully saved, False otherwise.
    """

    try:
        logger.info(f"Saving model to {file_name}...")
        with open(file_name, "w") as fout:
            fout.write(model_to_json(model))
        logger.info("Model saved successfully.")
        return True
    except Exception as e:
        logger.error(f"Failed to save model to {file_name}: {e}")
        return False  # Indicate failure


def read_model(file_name: str) -> prophet.models:
    """
    Reads a model from a file.

    Args:
        file_name (str): The path to the file containing the model.

    Returns:
        prophet.models: The loaded model.
    """

    try:
        logger.info(f"Reading model from {file_name}...")
        with open(file_name, "r") as fin:
            model = model_from_json(fin.read())
        logger.info("Model loaded successfully.")
    except FileNotFoundError:
        logger.warning(f"Model file {file_name} not found.")
        model = None

    return model


def predict_sort_and_save_data_dir(
    not_sorted_dir_path: str,
    sorted_dir_path: str,
    saved_data_format: List[str],
    data_freq: float,
    days_number: int,
) -> None:
    """
    Predicts, sorts, and saves data from a directory.

    Args:
        not_sorted_dir_path (str): The path to the directory containing the unsorted data.
        sorted_dir_path (str): The path to the directory where the sorted data will be saved.
        saved_data_format (List[str]): The format in which the data will be saved.
        data_freq (float): The frequency of the data (in hours).
        days_number (int): The number of days to consider for data processing.

    Returns:
        None
    """

    logger.info(f"Loading data from {not_sorted_dir_path}...")
    list_df = load_and_save_df.read_df_from_csv(not_sorted_dir_path)
    df = pd.concat(list_df)

    logger.info("Cleaning and filtering data...")
    df["problem_count"] = pd.to_numeric(df["problem_count"], errors="coerce")
    df["status"] = pd.to_numeric(df["status"], errors="coerce")

    df = df[(df["status"] > 399) & (df["status"] < 500)]

    df["date"] = pd.to_datetime(df.index)
    max_date = df["date"].max()
    cutoff_date = max_date - timedelta(days=days_number)
    df = df[df["date"] > cutoff_date]

    df.sort_index(inplace=True)

    min_date = df.index.min()
    max_date = df.index.max()
    full_date_range = pd.date_range(start=min_date, end=max_date, freq=data_freq)

    fill_entry = []
    for service, group in df.groupby(["service"]):
        service_df_user_count = group.drop_duplicates(subset="date")["user_count"]
        for status, status_group in group.groupby(["status"]):
            fill_entry.append(
                (
                    service_df_user_count,
                    full_date_range,
                    service[0],
                    status[0],
                    status_group,
                )
            )

    logger.info("Filling missing hours with multiprocessing...")
    num_processes = mp.cpu_count()
    df_filled = []

    try:
        with mp.Pool(processes=num_processes) as pool:
            chunk_size = max(1, len(fill_entry) // (num_processes * 10))
            for result in pool.imap_unordered(
                data_preparation.fill_missing_hours,
                fill_entry,
                chunksize=chunk_size,
            ):
                if result is not None:
                    df_filled.append(result)
    except Exception as e:
        logger.error(f"An error occurred during multiprocessing of fill_missing_hours: {e}")

    logger.info("Finished filling missing hours.")

    df = pd.concat(df_filled)

    logger.info("Calculating ratios...")
    df = data_preparation.ratio_calculation(df)

    df.drop(columns=["problem_count"], inplace=True)

    logger.info("Making predictions...")
    forecasted_df = predictions.make_predictions(df)

    logger.info(f"Saving forecasted data to {sorted_dir_path}...")
    for key, forecasted_group in forecasted_df.items():
        service, status = key
        forecasted_group["user_count"] = forecasted_group["user_count"].astype(int)

        load_and_save_df.save_df_to_csv(forecasted_group[saved_data_format], f"{service}_{status}", sorted_dir_path)


def list_of_service_status_for_zabbix_json(file_path: str, unique_pairs: pd.DataFrame) -> None:
    """
    Generate a JSON file containing the service names and their corresponding status for Zabbix.

    Args:
        file_path (str): The path to the output JSON file.
        unique_pairs (pd.DataFrame): A DataFrame containing unique pairs of service and status.

    Returns:
        None
    """

    logger.info(f"Generating Zabbix JSON file at {file_path}...")
    data = []
    for _, row in unique_pairs.iterrows():
        service = row["service"]
        corrected_service = service.replace("-", "_")
        status = row["status"]

        data.append({"{#SVCNAME}": corrected_service, "{#STNAME}": status})

    # Add 5XX status
    error_list = []
    service_list = unique_pairs[["service"]].drop_duplicates().reset_index(drop=True)
    for index, row in service_list.iterrows():
        corrected_service = row["service"].replace("-", "_")
        for error_status in range(500, 508 + 1):
            error_list.append({"{#SVCNAME}": corrected_service, "{#STNAME}": error_status})

    data = data + error_list

    # Serializing json
    json_object = json.dumps(data, indent=4)

    with open(file_path, "w") as outfile:
        outfile.write(json_object)
    logger.info("Zabbix JSON file generated successfully.")


def clean_directory(path: str) -> None:
    """
    Clean all contents of a directory.

    Args:
        path (str): The path of the directory to be cleaned.

    Returns:
        None
    """
    logger.info(f"Cleaning contents of: {path}")
    # Ensure the directory exists
    if not os.path.exists(path):
        logger.error(f"Cleaning content: Directory does not exist: {path}")
        return

    # Iterate over each item in the directory
    for item in os.listdir(path):
        item_path = os.path.join(path, item)

        # Remove files
        if os.path.isfile(item_path) or os.path.islink(item_path):
            os.unlink(item_path)  # Use unlink for files and symlinks
        # Remove directories
        elif os.path.isdir(item_path):
            shutil.rmtree(item_path)

    logger.info(f"Cleaned all contents of: {path}")


def transfer_dir(sorted_data_path: str, new_sorted_data_path: str) -> None:
    """
    Transfers the directory by replacing the existing sorted_data directory with a new one.

    Args:
        sorted_data_path (str): The path of the current directory.
        new_sorted_data_path (str): The path of the new directory.

    Returns:
        None
    """
    logger.info(f"Starting transfer of model from '{new_sorted_data_path}' to '{sorted_data_path}'.")

    # If the current model directory exists, delete it and its contents
    if os.path.exists(sorted_data_path):
        logger.info(f"Existing model directory found: {sorted_data_path}. Deleting it.")
        shutil.rmtree(sorted_data_path)
        logger.info(f"Deleted existing model directory: {sorted_data_path}.")

    # Rename the new model directory to the current model directory
    try:
        os.rename(new_sorted_data_path, sorted_data_path)
        logger.info(f"Renamed '{new_sorted_data_path}' to '{sorted_data_path}'.")
    except OSError as e:
        logger.error(f"Failed to rename '{new_sorted_data_path}' to '{sorted_data_path}': {e}")


def rsync_files(remote_path: str, local_path: str, port: str) -> None:
    """
    Synchronizes remote files to the local machine using rsync.

    Args:
        remote_path (str): The path of the remote files to be synchronized.
        local_path (str): The path of the local directory where the files will be synchronized to.
        port (str): The port number for the remote server.

    Raises:
        subprocess.CalledProcessError: If the rsync command fails.

    Returns:
        None
    """
    logger.info(f"Starting rsync from {remote_path} to {local_path}...")
    try:
        subprocess.run(
            [
                "rsync",
                "-avz",
                "-e",
                f"ssh -o StrictHostKeyChecking=no -p {port}",
                "--no-perms",
                "--no-owner",
                "--no-group",
                "--no-times",
                remote_path,
                local_path,
            ],
            check=True,
            capture_output=True,
            text=True,
        )
        logger.info("Rsync completed successfully.")
    except subprocess.CalledProcessError as e:
        logger.error(f"Rsync failed: {e.stderr}")


def rsync_end_of_training() -> None:
    """
    Synchronizes necessary files and directories to a remote server at the end of training.

    Args:
        None

    Returns:
        None
    """

    # Rsync paths
    paths_to_sync = [
        (
            zabbix_service_status_list_file_in_training_serv,
            to_rsync_zabbix_service_status_list_file,
        ),
        (training_save_model_path, to_rsync_save_model_path),
        (sorted_data_path_in_training_serv, to_rsync_sorted_data_path),
    ]

    logger.info("Starting to sync files after training...")
    for local_path, remote_path in paths_to_sync:
        logger.info(f"Syncing {local_path} to {remote_monitoring_server}:{remote_path}")
        rsync_files(
            local_path,
            f"{remote_monitoring_server}:{remote_path}",
            remote_monitoring_port,
        )
    logger.info("Syncing after training completed successfully.")


def copy_blacklist_from_remote() -> None:
    """
    Copies the blacklist file from the remote server to the current server.

    Args:
        None

    Returns:
        None
    """

    logger.info(f"Starting to copy blacklist from {remote_monitoring_server}:{from_rsync_blacklist_path} to {blacklist_path_copy}...")
    try:
        rsync_files(
            f"{remote_monitoring_server}:{from_rsync_blacklist_path}",
            blacklist_path_copy,
            remote_monitoring_port,
        )
        logger.info("Blacklist copied successfully.")
    except Exception as e:
        logger.error(f"Failed to copy blacklist: {e}")
