import json
import os
from datetime import datetime
from typing import Dict, Optional

import yaml
from services.logger.logging_service import get_logger

logger = get_logger(__name__)


class ZabbixManager:
    def __init__(self, zabbix_result_path: str, frontend_result_dir: str) -> None:
        """
        Initialize the ZabbixManager class.

        :param zabbix_result_path: The path to the Zabbix result file.
        :param frontend_result_dir: The directory to store the frontend result files.
        """
        self.zabbix_result_path = zabbix_result_path
        self.zabbix_result: Dict[str, Dict[str, int]] = {}
        self.old_zabbix_result: Dict[str, Dict[str, int]] = {}

        self.frontend_result_dir = frontend_result_dir
        self.frontend_result: Dict[str, Dict[str, Optional[int]]] = {}

        self.max_alert: int = 2
        self.low_alert: int = 1
        self.no_alert: int = 0

        logger.info(
            "ZabbixManager initialized with result paths: %s and %s",
            zabbix_result_path,
            frontend_result_dir,
        )

    def set_result(
        self,
        service: str,
        status: str,
        alerting_level: int,
        user_count: Optional[int] = None,
        problem_percentage: Optional[float] = None,
        problem_count: Optional[int] = None,
        cdn: Optional[str] = None,
    ) -> None:
        """
        Set the result for a specific service and status.

        :param service: The service name.
        :param status: The status of the service.
        :param alerting_level: The alerting level.
        :param user_count: The user count (optional).
        :param problem_percentage: The problem percentage (optional).
        :param problem_count: The problem count (optional).
        :param cdn: The CDN (optional).
        """
        logger.debug(
            "Setting result for service '%s' with status '%s' and alerting level %d",
            service,
            status,
            alerting_level,
        )

        if service not in self.zabbix_result:
            self.zabbix_result[service] = {}
        self.zabbix_result[service][status] = alerting_level

        key = f"{service}_{status}"
        if key not in self.frontend_result:
            self.frontend_result[key] = {}
        self.frontend_result[key]["alerting_level"] = alerting_level
        self.frontend_result[key]["user_count"] = user_count
        self.frontend_result[key]["problem_percentage"] = problem_percentage
        self.frontend_result[key]["service"] = service
        self.frontend_result[key]["status"] = status
        self.frontend_result[key]["problem_count"] = problem_count
        self.frontend_result[key]["cdn"] = cdn

        logger.debug("Result set successfully for '%s' with status '%s'", service, status)

    def has_already_be_seen(self, service: str, status: str) -> Optional[int]:
        """
        Check if the service and status have already been seen.

        :param service: The service name.
        :param status: The status of the service.
        :return: The alerting level if the service and status have been seen, None otherwise.
        """
        logger.debug("Checking if '%s' with status '%s' has already been seen", service, status)
        if service in self.old_zabbix_result and status in self.old_zabbix_result[service]:
            logger.debug("Found previous result for '%s' with status '%s'", service, status)
            return self.old_zabbix_result[service][status]
        else:
            logger.debug("No previous result found for '%s' with status '%s'", service, status)
            return None

    def save_result(self, date: str) -> None:
        """
        Save the results to JSON files.

        :param date: The date in 'YYYY-MM-DD HH:MM:SS' format.
        """
        logger.info("Saving results for date: %s", date)

        try:
            self.old_zabbix_result = self.zabbix_result
            self._modify_service_keys()
            json_object = json.dumps(self.zabbix_result, indent=4)
            with open(self.zabbix_result_path, "w") as file:
                file.write(json_object)
            logger.info("Zabbix results saved to '%s'", self.zabbix_result_path)
            self.zabbix_result = {}
        except yaml.YAMLError as e:
            logger.error("Error updating Zabbix JSON file: %s", e)
            raise ValueError(f"Error updating JSON file: {e}")

        try:
            if not os.path.exists(self.frontend_result_dir):
                os.makedirs(self.frontend_result_dir)
                logger.info("Created frontend result directory: %s", self.frontend_result_dir)

            json_object = json.dumps(self.frontend_result, indent=4)
            with open(f"{self.frontend_result_dir}/{self._transform_date(date)}.json", "w") as file:
                file.write(json_object)
            logger.info("Frontend results saved to '%s'", self.frontend_result_dir)
            self.frontend_result = {}
        except yaml.YAMLError as e:
            logger.error("Error updating frontend JSON file: %s", e)
            raise ValueError(f"Error updating JSON file: {e}")

    def _modify_service_keys(self) -> None:
        """
        Modify the service keys in the zabbix_result dictionary.
        """
        logger.debug("Modifying service keys in zabbix_result dictionary")
        updated_data = {}
        for service, value in self.zabbix_result.items():
            new_service = service.replace("-", "_")
            updated_data[new_service] = value
        self.zabbix_result = updated_data
        logger.debug("Service keys modified successfully")

    def _transform_date(self, input_date: str) -> str:
        """
        Transforms a date string from 'YYYY-MM-DD HH:MM:SS' format to 'YYYY_MM_DD_HH_MM' format.

        :param input_date: The input date string in 'YYYY-MM-DD HH:MM:SS' format.
        :return: The transformed date string in 'YYYY_MM_DD_HH_MM' format.
        """
        try:
            dt = datetime.strptime(input_date, "%Y-%m-%d %H:%M:%S")
            transformed_date = dt.strftime("%Y_%m_%d_%H_%M")
            return transformed_date
        except ValueError as e:
            logger.error("Invalid date format: %s. Expected 'YYYY-MM-DD HH:MM:SS'.", input_date)
            raise ValueError(f"Invalid date format: {input_date}. Expected 'YYYY-MM-DD HH:MM:SS'.") from e
