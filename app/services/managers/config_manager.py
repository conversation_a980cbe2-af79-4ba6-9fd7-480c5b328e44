import os
from multiprocessing import Manager

import yaml
from services.logger.logging_service import get_logger

logger = get_logger(__name__)


class ConfigManager:
    """
    ConfigManager class for managing configuration settings.

    Attributes:
        settings_path (str): The file path to the settings configuration file.
        fixerrors_path (str): The file path to the fix errors configuration file.
        manager (Manager): The multiprocessing Manager object.
        service_status_settings (dict): The shared dictionary for service status settings.
        service_status_for_fixerrors_settings (dict): The shared dictionary for fix errors settings.
        sensitivity_factor_ref (int): The reference sensitivity factor.
        threshold_ref (int): The reference threshold.

    Methods:
        __init__(settings_path: str, fixerrors_path: str): Initializes a ConfigManager object.
        _load_config(path: str) -> dict: Load the configuration from a YAML file.
        get_settings(service: str, status: int) -> int: Retrieves the settings for a given service and status.
        get_fixerrors_settings(service: str, status: int) -> int: Retrieves the fix errors settings for a given service and status.
        save() -> None: Save the configuration settings to YAML files.
        load() -> None: Reloads the configuration settings for the service status and fix errors.
    """

    def __init__(self, settings_path: str, fixerrors_path: str):
        """
        Initializes a ConfigManager object.

        Args:
            settings_path (str): The file path to the settings configuration file.
            fixerrors_path (str): The file path to the fix errors configuration file.
        """

        self.settings_path = settings_path
        self.fixerrors_path = fixerrors_path

        self.manager = Manager()
        self.service_status_settings = self.manager.dict()
        self.service_status_for_fixerrors_settings = self.manager.dict()

        self.sensitivity_factor_ref: int = 8
        self.threshold_ref: int = 5

        logger.info(f"ConfigManager initialized with settings path: {settings_path} " f"and fixerrors path: {fixerrors_path}")

    def _load_config(self, path: str) -> dict:
        """
        Load the configuration from a YAML file.

        Args:
            path (str): The path to the YAML file.

        Returns:
            dict: The loaded configuration as a dictionary.

        Raises:
            ValueError: If there is an error reading the YAML file.
        """

        if not os.path.exists(path):
            logger.warning(f"Configuration file at '{path}' not found. Creating a new file.")
            with open(path, "w") as file:
                yaml.safe_dump({}, file)

        with open(path, "r") as file:
            try:
                logger.info(f"Loading configuration from '{path}'")
                return yaml.safe_load(file) or {}
            except yaml.YAMLError as e:
                logger.error(f"Error reading YAML file at '{path}': {e}")
                raise ValueError(f"Error reading YAML file at '{path}': {e}")

    def get_settings(self, service: str, status: int) -> int:
        """
        Retrieves the settings for a given service and status.

        Args:
            service (str): The name of the service.
            status (int): The status code.

        Returns:
            int: The sensitivity factor for the service and status.
        """

        key = f"{service}_{status}"
        client_settings = self.service_status_settings.get(key)

        if client_settings is None:
            logger.info(f"Settings for {service}_{status} not found, using default sensitivity factor.")
            c = {}
            c[key] = self.sensitivity_factor_ref
            self.service_status_settings.update(c)
            return self.sensitivity_factor_ref

        logger.info(f"Retrieved settings for {service}_{status}: {client_settings}")
        return client_settings

    def get_fixerrors_settings(self, service: str, status: int) -> int:
        """
        Retrieves the fix errors settings for a given service and status.

        Parameters:
            service (str): The name of the service.
            status (int): The status of the service.

        Returns:
            int: The threshold for fix errors for the specified service and status.
        """
        service_status_key = f"{service}_{status}"
        client_settings = self.service_status_for_fixerrors_settings.get(service_status_key)

        if client_settings is None:
            logger.info(f"Fix errors settings for {service}_{status} not found, using default threshold.")
            c = {}
            c[service_status_key] = self.threshold_ref
            self.service_status_for_fixerrors_settings.update(c)
            return self.threshold_ref

        logger.info(f"Retrieved fix errors settings for {service}_{status}: {client_settings}")
        return client_settings

    def save(self) -> None:
        """
        Save the configuration settings to YAML files, ensuring any modifications made
        during processing are preserved by merging with the existing file contents.

        Raises:
            ValueError: If there is an error saving the YAML configuration.
        """
        try:
            logger.info("Saving configuration settings...")

            # Load existing configurations from the file
            existing_settings = self._load_config(self.settings_path)
            existing_fixerrors = self._load_config(self.fixerrors_path)

            # Merge existing settings with in-memory settings
            merged_settings = {
                **dict(self.service_status_settings),
                **existing_settings,
            }
            merged_fixerrors = {
                **dict(self.service_status_for_fixerrors_settings),
                **existing_fixerrors,
            }

            # Save merged configurations back to the file
            logger.info(f"Saving merged settings to '{self.settings_path}'")
            with open(self.settings_path, "w") as file:
                yaml.safe_dump(merged_settings, file, default_flow_style=False)

            logger.info(f"Saving merged fix errors settings to '{self.fixerrors_path}'")
            with open(self.fixerrors_path, "w") as file:
                yaml.safe_dump(merged_fixerrors, file, default_flow_style=False)

            logger.info("Configuration settings saved successfully.")

        except yaml.YAMLError as e:
            logger.error(f"Error saving YAML configuration: {e}")
            raise ValueError(f"Error saving YAML configuration: {e}")

    def load(self) -> None:
        """
        Load the configuration settings from YAML files into shared dictionaries.
        """
        logger.info("Loading configuration settings...")

        # Load data from files
        settings_data = self._load_config(self.settings_path)
        fixerrors_data = self._load_config(self.fixerrors_path)

        self.service_status_settings = self.manager.dict()
        self.service_status_for_fixerrors_settings = self.manager.dict()

        # Update the shared dictionaries
        self.service_status_settings.update(settings_data)
        self.service_status_for_fixerrors_settings.update(fixerrors_data)

        logger.info("Configuration settings loaded successfully.")
