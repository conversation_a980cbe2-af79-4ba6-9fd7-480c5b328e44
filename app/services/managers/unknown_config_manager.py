import os
from typing import Any, Dict

import yaml
from services.logger.logging_service import get_logger

logger = get_logger(__name__)


class UnknownConfigManager:
    """
    A class for managing unknown configuration values stored in a YAML file.
    """

    def __init__(self, file_path: str) -> None:
        """
        Initializes the UnknownConfigManager instance.

        Args:
            file_path (str): The path to the YAML file.
        """
        self.file_path = file_path
        self.config: Dict[str, Any] = {}
        self.default_value: int = 15
        logger.info(f"Initialized UnknownConfigManager with file path: {file_path}")

    def _load(self, file_path) -> None:
        """
        Loads the configuration values from the YAML file.
        If the file doesn't exist, creates an empty file.

        Args:
            file_path (str): Path to the YAML file to load.

        Returns:
            dict: The loaded configuration dictionary.
        """
        logger.info(f"Loading configuration from {file_path}")
        if not os.path.exists(file_path):
            logger.warning(f"File does not exist. Creating a new empty file at {file_path}")
            with open(file_path, "w") as file:
                yaml.safe_dump({}, file)
        with open(file_path, "r") as file:
            try:
                config_data = yaml.safe_load(file) or {}
                logger.info("Configuration successfully loaded.")
                return config_data
            except yaml.YAMLError as e:
                logger.error(f"Error reading YAML file at '{file_path}': {e}")
                raise ValueError(f"Error reading YAML file at '{file_path}': {e}")

    def load(self) -> None:
        """
        Calls _load to load the configuration values from the YAML file.
        If the file doesn't exist, creates an empty file.
        """
        logger.info("Loading configuration into memory.")
        self.config = self._load(self.file_path)

    def get_value(self, service: str, status: str) -> int:
        """
        Retrieves the value for a given service and status.

        Args:
            service (str): The service name.
            status (str): The status name.

        Returns:
            int: The value associated with the service and status.
        """
        key = f"{service}_{status}"
        value = self.config.get(key)

        if value is None:
            logger.info(f"No value found for key '{key}'. Using default value: {self.default_value}")
            self.config[key] = self.default_value
            return self.default_value

        logger.info(f"Retrieved value for '{key}': {value}")
        return value

    def save(self) -> None:
        """
        Saves the configuration values to the YAML file.
        """
        try:
            # Load existing configurations from the file
            existing_settings = self._load(self.file_path)

            # Merge existing settings with in-memory settings
            merged_settings = {
                **dict(self.config),
                **existing_settings,
            }

            with open(self.file_path, "w") as file:
                yaml.safe_dump(
                    merged_settings,
                    file,
                    default_flow_style=False,
                )
            logger.info(f"Configuration saved successfully to {self.file_path}")

        except yaml.YAMLError as e:
            logger.error(f"Error saving YAML configuration: {e}")
            raise ValueError(f"Error saving YAML configuration: {e}")
