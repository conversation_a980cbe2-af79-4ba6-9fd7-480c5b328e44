import logging
import logging.config
import os

from yaml import safe_load


# Try to load the logger configuration file
def load_config():
    """
    Loads the logger configuration file and configures the loggers accordingly.
    If the logger configuration file is not found, it uses the default configuration.
    If the logger configuration file is found but there is an error loading it, it logs the error.

    Raises:
        FileNotFoundError: If the logger configuration file is not found.
        Exception: If there is an error loading the logger configuration file.
    """
    try:
        # Load the logger configuration file
        config_path = os.path.join(os.path.dirname(__file__), "logging_config.yaml")
        log_config = safe_load(open(config_path))

        logging.config.dictConfig(log_config)
    # If the logger configuration file is not found, use the default configuration
    except FileNotFoundError:
        logging.basicConfig(level=logging.DEBUG)
        logging.warning("No logger configuration file found. Using default configuration.")
    # If the logger configuration file is found but there is an error loading it, log the error
    except Exception as e:
        logging.basicConfig(level=logging.DEBUG)
        logging.error(f"Failed to load logger configuration file: {e}")


def get_logger(name: str) -> logging.Logger:
    """
    Returns a logger with the specified name.

    Args:
        name: The name of the logger.

    Returns:
        The logger with the specified name.
    """
    return logging.getLogger(name)
