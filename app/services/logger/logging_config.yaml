version: 1
disable_existing_loggers: False

formatters:
  default:
    format: "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

handlers:
  console:
    class: logging.StreamHandler
    formatter: default
    stream: ext://sys.stdout

  file:
    class: logging.FileHandler
    formatter: default
    filename: /workdir/app.log
    encoding: utf-8
    mode: a

  null_handler:
    class: logging.NullHandler

loggers:
  cmdstanpy:
    level: WARNING
    handlers:
      - null_handler
    propagate: false

  prophet:
    level: WARNING
    handlers:
      - null_handler
    propagate: false

  root:
    level: INFO
    handlers: [console, file]
    propagate: true
