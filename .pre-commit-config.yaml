repos:
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        args: [--line-length=150]

  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        args: [--max-line-length=150, "--ignore=E203,W503"]


  - repo: https://github.com/timothycrosley/isort
    rev: 5.13.2
    hooks:
      - id: isort
        additional_dependencies: [toml]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: debug-statements
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
