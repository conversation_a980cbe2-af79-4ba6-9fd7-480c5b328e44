# ===========================
# Load and Train Variables
# ===========================
#following variables do NOT need to change:
directory_path='/datas/my_datas/' #load csv data
zabbix_service_status_list_file_in_training_serv='/datas/zabbix_service_status_list_file_in_training_serv.json' #list of all service-status for zabbix
training_save_model_path='/datas/my_models_while_training/' #name of dir where to save models
sorted_data_path_in_training_serv='/datas/sorted_datas_path_in_training_serv/' #name of dir where to save processed csv datas
blacklist_path_copy='/datas/blacklist.json'
#following variables are to fill :
username='druid_username' #username to access druid api
password='druid_password' #password to access druid api
remote_monitoring_server='user@ip' #server where project logs is deployed
remote_monitoring_port='5142' #port where to access server where project logs is deployed
#for the following you need to find full path where these are saved in project logs .env and docker-compose, make sure the path are correct :
from_rsync_blacklist_path='/to_fill/blacklist.json' #blacklist_path
to_rsync_save_model_path='/to_fill/my_models' #save_model_path
to_rsync_sorted_data_path='/to_fill/new_sorted_datas' #new_sorted_datas_path
to_rsync_zabbix_service_status_list_file='/to_fill/outlier_detection_service_status_list_file.json' #/zabix/

# ===========================
# Log Variables
# ===========================
#following variables do NOT need to change:
config_file_path='/configs/config.yaml' #config 4XX
config_file_for_fixerrors_path='/configs/config_file_for_fixerrors.yaml' #config 5XX
unknown_path='/configs/unknown_config.yaml'
manual_config_path='/configs/manual_config.yaml'
blacklist_path='/configs/blacklist.json'
plot_save_path='/datas/my_plots/' #save graph plots
zabbix_result_file='/zabbix/outlier_detection_result_file.json' #zabbix result
frontend_result_dir='/datas/frontend_results/' #front result dir
logs_path='/log_swap_storage/'
save_model_path='/datas/my_models/' #models
sorted_data_path='/datas/sorted_datas/'
new_sorted_data_path='/datas/new_sorted_datas/' #data processed by 'train'
#following variables should NOT change except if cdn logs path change. if you do change then also change Dockerfile part with known_hosts
#fill username in following
remote_logs_path='fill_user@************:/data1/rsyslog-ingest/main/' #server to rsync logs-cdn
remote_logs_port='5142' #port of server logs-cdn
