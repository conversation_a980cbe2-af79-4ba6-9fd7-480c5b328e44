name: outlier-detection-in-time-series

x-common-context: &common-context
  build:
    context: .
  env_file: .env
  restart: unless-stopped

services:
  log:
    <<: *common-context
    command: log
    volumes:
      #to fill
      - /home/<USER>/test_volume/:/log_swap_storage
      - /home/<USER>/druid_problem/rhassam/:/datas #modify this = modify docker-compose of front-outlier-detection
      - /etc/zabbix/zabbix_scripts/:/zabbix
      - /home/<USER>/druid_problem/rhassam/config_files/:/configs #modify this = modify docker-compose of front-outlier-detection

  # both load and train at the same time, easier for cron
  load_and_train:
    <<: *common-context
    command: load_and_train
    volumes:
      #to fill
      - /absolute_path_of_dir_where_to_save_datas/:/datas

  # if you want to separately load or train
  train:
    <<: *common-context
    command: train
    volumes:
      #to fill, should be same as load
      - /absolute_path_of_dir_where_to_save_datas/:/datas

  load:
    <<: *common-context
    command: load
    volumes:
      #to fill, should be same as train
      - /absolute_path_of_dir_where_to_save_datas/:/datas
