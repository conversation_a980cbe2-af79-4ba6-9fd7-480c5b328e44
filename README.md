# outlier-detection-in-time-series - services monitoring

By employing Facebook's Prophet library for forecasting and multiprocessing techniques, the system can detect outliers in real-time data across multiple services and status for monitoring purposes.\
This project consists of 2 part :

- "load_and_train" which will load the data and train the models (recommendation is to use this part once per month).
- "log" which is the main part of the project and will be used 24/7. This part will use the data previously loaded and the trained model to detect and report errors in services and status results.

There also is a [front part to deploy](https://kvasir.hexaglobe.net/hexaglobe/front-outlier-detection) for this project to work as expected.

---

## Deployment

### Server requirements

- Ensure the server where you deploy the 'load_and_train' container of this project has access to the druid database, otherwise you will not be able to load data from it. Be aware that the train part will use a lot of RAM and CPU power and can last up to 24 hours. You can Downgrade models quality by changing the "fourier_order" of model inside the 'initialize_model()' function in the train_model_and_forecast.py file. This will worsen the monitoring quality but will take less time.

- Ensure you deploy the front part of the project (front-outlier-detection) in the same server as the 'log' container of this project, otherwise the front will not be able to access to this python's result files and thus will not be able to work.

---

### Prerequisites

Ensure the following are installed:

- Python 3.8+
- pip

### Installation Steps

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd outlier-detection-in-time-series/
   ```

2. Create and activate a virtual environment:

```bash
python3 -m venv venv
source venv/bin/activate
```

3. Fill paths in docker-compose.yml. \
you ONLY need to fill paths of command you will use on this server (log or load_and_train). \
for logs this should be the same paths than front-end docker-compose.yml

4. Configure environment variables:\
   Duplicate the `.env_template` file, rename it to `.env`, and fill the path variables in it.\
   you ONLY need to fill paths of command you will use on this server (log or load_and_train).
```bash
cp .env_template .env
```

### Create SSH Key

Need to create ssh key for both servers. if not, create key with
```bash
ssh-keygen -t rsa -b 2048
```
and copy the ```id_rsa``` in repo (same level as Dockerfile). Also, check if the key that is being used for rsync is in the ```authorized keys``` file of the server it is trying to access. Missing ssh key can create rsync problems.
```bash
cp ~/.ssh/id_rsa .
```

### Running the System

First build the container :

  ```bash
  sudo docker compose build
  ```

Then use docker compose with one of the following commands:

- **Data loading and Model training:**
Both load then train, best option to use. --days is optionnal.

  ```bash
  sudo docker compose up load_and_train [--days <number-of-days>]
  ```

- **Log processing:**
log is to monitor services. Model needs to be train before.

  ```bash
  sudo docker compose up log
  ```

you can also use the following commands if you only want to load or train (not recommended):

- **Data loading:**
load is to load newest data from druid to prepare the training.

  ```bash
  sudo docker compose up load
  ```

- **Model training:**
train is to train the model on data. Recommend to load for new data before. --days is optionnal.

  ```bash
  sudo docker compose up train [--days <number-of-days>]
  ```

### Arguments

- `--days`: Number of past days to save for log processing (default: 45). Recommend to not change this parameter.

### Handling Missing Data

- Missing time intervals are automatically filled with zeroes for consistency.

### Error Handling

- Logs any issues related to missing files, improperly formatted data, or missing environment variables.

---

## Results

### Zabbix results

Results are write in file for zabbix to read, zabbix will then launch error status if problem occures.

```json
{
  "service":
    {
      "status": int  // Alert level: 1 (minor) or 2 (major)
    }
}
```

### Front results

json used by the front

```json
[
  {
    "alerting_level": int,      // Alert level: 1 (minor) or 2 (major)
    "user_count": int,          // Number of affected users
    "problem_percentage": int,  // Percentage of users experiencing this issue
    "service": string,          // Name of the service
    "status": int,              // Status of problem
    "problem_count": int,       // Number of reported problems
    "cdn":                      // infos about each CDN
          {
              "name": string,                     // CDN name
              "ip": string,                       // CDN IP
              "cdn_count": int,                   // Number of problem that got from this CDN
              "percentage": float,                // Percentage of problem
              "valid_cdn_count": int,             // Number of valid request that got from this CDN
              "valid_percentage_count": float     // Percentage of valid request
          }
  }
]
```

---

## Possible problems cause

- if "log" stops it can be because of two factors. either the rsync failed (mostly ssh key problem, refer you to section 'Need to do for project to work') or there is a problem into the rsyslog-ingest (cdn file missing, alert analytics).
- if "load_and_train" stops it can be because of rsync failed (mostly ssh key problem, refer you to section 'Need to do for project to work').
- Due to ownership permissions, it is possible that you need to give writing permissions to dir and file (for exemple chmod 777) that the container will use. It is most likely the cause of the problem if container can not write or save anything without apparent reasons (both for current and remote server).

### Options you can modify

- Plot : you can chose to activate plotting, every services will then be plotted with his data history and model forecast. You can then visually see evolution of service errors and model quality. Activate plotting by changing ```to_plot``` variable to True in server_logs.py

### Config files details

- For 400-series errors the values are used to fine-tune the model for the corresponding service-status. (e.g : equidia-live2-live_404 is set to 10, it will be raising the error detection bar of the trained model, highlighting less errors)
- For 500-series errors, unknown errors, and manualy-added errors the values are used to set a threshold. If error numbers exceed this threshold, program launch an error. (e.g : equidia-live2-live_503 is set to 20, if number of 503 for service equidia-live2-live is greater or equal than 20, it launch error)
