FROM python:3.12-slim AS build

# Create a virtual environment
RUN python3.12 -m venv /root/venv
ENV PATH="/root/venv/bin:$PATH"

# Activate virtual environment and upgrade pip
RUN pip install --upgrade pip

# Set working directory for Python dependencies installation
WORKDIR /workdir

# Copy and install Python dependencies with caching enabled
COPY requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade pip && \
    pip install -r requirements.txt

FROM python:3.12-slim AS runtime

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    VIRTUAL_ENV="/root/venv" \
    PATH="/root/venv/bin:$PATH" \
    GRADIO_SERVER_NAME="0.0.0.0"

# Set up working directory
WORKDIR /workdir

RUN mkdir -p /log_swap_storage /zabbix /configs /datas/sorted_datas

# Install rsync, SSH client, and OpenSSH (for ssh-keygen)
RUN --mount=type=cache,target=/var/cache/apt \
    --mount=type=cache,target=/var/lib/apt \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    rsync openssh-client && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy the virtual environment
COPY --chmod=755 --from=build $VIRTUAL_ENV $VIRTUAL_ENV

# Copy the rest of the application code
COPY ./app /workdir/app
COPY ./*.yaml /workdir/

# Set up SSH keys for root
RUN mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh

# Copy the SSH keys into the container
COPY ./id_ed25519 /root/.ssh/id_ed25519

RUN ssh-keyscan -p 5142 -H *************** > /root/.ssh/known_hosts && \
    chmod 600 /root/.ssh/id_ed25519

# Command to run your application (e.g., rsync file operation)
ENTRYPOINT ["python", "app/main.py"]

# Default command
CMD ["log"]
