#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile requirements.in
#
certifi==2024.8.30
    # via requests
charset-normalizer==3.3.2
    # via requests
cmdstanpy==1.2.4
    # via prophet
contourpy==1.3.0
    # via matplotlib
cycler==0.12.1
    # via matplotlib
dill==0.3.9
    # via multiprocess
fonttools==4.54.1
    # via matplotlib
h5py==3.12.1
    # via -r requirements.in
holidays==0.57
    # via prophet
idna==3.10
    # via requests
importlib-resources==6.4.5
    # via prophet
joblib==1.4.2
    # via
    #   scikit-learn
    #   tslearn
kiwisolver==1.4.7
    # via matplotlib
llvmlite==0.43.0
    # via numba
matplotlib==3.9.2
    # via
    #   -r requirements.in
    #   prophet
    #   seaborn
multiprocess==0.70.17
    # via -r requirements.in
numba==0.60.0
    # via tslearn
numpy==2.0.2
    # via
    #   -r requirements.in
    #   cmdstanpy
    #   contourpy
    #   h5py
    #   matplotlib
    #   numba
    #   pandas
    #   prophet
    #   scikit-learn
    #   scipy
    #   seaborn
    #   stanio
    #   tslearn
packaging==24.1
    # via
    #   matplotlib
    #   plotly
pandas==2.2.3
    # via
    #   -r requirements.in
    #   cmdstanpy
    #   prophet
    #   seaborn
pillow==10.4.0
    # via matplotlib
plotly==5.24.1
    # via -r requirements.in
prophet==1.1.6
    # via -r requirements.in
pyparsing==3.1.4
    # via matplotlib
python-dateutil==2.9.0.post0
    # via
    #   holidays
    #   matplotlib
    #   pandas
python-dotenv==1.0.1
    # via -r requirements.in
pytz==2024.2
    # via
    #   -r requirements.in
    #   pandas
pyyaml==6.0.2
    # via -r requirements.in
requests==2.32.3
    # via -r requirements.in
ripgrep==14.1.0
    # via -r requirements.in
scikit-learn==1.5.2
    # via
    #   -r requirements.in
    #   tslearn
scipy==1.14.1
    # via
    #   scikit-learn
    #   tslearn
seaborn==0.13.2
    # via -r requirements.in
six==1.16.0
    # via python-dateutil
stanio==0.5.1
    # via cmdstanpy
tenacity==9.0.0
    # via plotly
threadpoolctl==3.5.0
    # via scikit-learn
tqdm==4.66.5
    # via
    #   -r requirements.in
    #   cmdstanpy
    #   prophet
tslearn==0.6.3
    # via -r requirements.in
tzdata==2024.2
    # via pandas
urllib3==2.2.3
    # via
    #   -r requirements.in
    #   requests
